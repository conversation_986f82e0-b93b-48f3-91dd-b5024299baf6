import { postData } from '@quidgest/clientapp/network'

// Cria um novo registo DOCUM com ficheiro associado (multi-upload)
export async function criarDocumComFicheiro(file, { nome, descricao, parentId, parentTable, relPath }) {
	const formData = new FormData();
	formData.append('File', file); // Nome igual ao DTO
	formData.append('Nome', nome || file.name);
	if (descricao) formData.append('Descricao', descricao);
	// Só adiciona ParentId se for uma string não vazia (GUID)
	if (typeof parentId === 'string' && parentId.trim() !== '') {
		formData.append('ParentId', parentId);
	}
	if (typeof parentTable === 'string' && parentTable.trim() !== '') {
		formData.append('ParentTable', parentTable);
	}
	// Adicionar RelPath se fornecido (para estrutura de pastas)
	if (typeof relPath === 'string' && relPath.trim() !== '') {
		formData.append('RelPath', relPath);
	}

	// Debug: mostra todos os campos do FormData
	//for (let [key, value] of formData.entries()) {
	//	console.log(`FormData: ${key} =`, value);
	//}

	// Embrulha o postData numa Promise para garantir resultado
	return new Promise((resolve, reject) => {
		postData(
			'DOCUM_MultiUpload',
			'MultiUploadCreate',
			formData,
			//{
			//	maxContentLength: Infinity,
			//	maxBodyLength: Infinity,
			//	timeout: 3600000 // Opcional: 1 hora, se esperas uploads lentos
			//},
			(data, response) => {
				const result = (response && response.data) || data;
				if (!result) {
					reject(new Error('Resposta inválida da API ao criar DOCUM'));
				} else {
					resolve(result);
				}
			},
			(err) => {
				console.error('Erro ao criar DOCUM:', err);
				if (err && err.response) {
					console.error('Resposta do backend:', err.response.data);
					alert('Erro do backend: ' + JSON.stringify(err.response.data));
				}
				reject(err);
			}
		);
	});
}

/**
 * Extrai o caminho relativo a partir do webkitRelativePath
 */
export function extractRelativePath(webkitRelativePath, baseFolderName) {
	try {
		// Normalizar separadores para /
		const normalizedPath = webkitRelativePath.replace(/\\/g, '/');

		// Encontrar a posição da pasta base
		const baseFolderIndex = normalizedPath.indexOf(baseFolderName + "/");
		if (baseFolderIndex === -1) {
			// Se não encontrar a pasta base, usar apenas o nome da pasta base
			return baseFolderName;
		}

		// Extrair o caminho a partir da pasta base (incluindo a pasta base)
		const pathFromBase = normalizedPath.substring(baseFolderIndex);

		// Remover o nome do ficheiro para obter apenas o caminho da pasta
		const lastSlashIndex = pathFromBase.lastIndexOf('/');
		if (lastSlashIndex > 0) {
			return pathFromBase.substring(0, lastSlashIndex);
		}

		// Se não há subpastas, retornar apenas a pasta base
		return baseFolderName;
	} catch (error) {
		console.error('Erro ao extrair caminho relativo:', error);
		// Em caso de erro, usar apenas a pasta base
		return baseFolderName;
	}
}

// Multi-upload: processa vários ficheiros
export async function gravarTodosOsDocum(documList) {
	const results = [];
	for (const { file, nome, descricao, parentId, parentTable, relPath } of documList) {
		try {
			const result = await criarDocumComFicheiro(file, { nome, descricao, parentId, parentTable, relPath });
			results.push({ fileName: file.name, ...result });
		} catch (err) {
			results.push({ fileName: file.name, success: false, error: err.message });
		}
	}
	return results;
}

// Busca o tamanho máximo de upload permitido pelo backend (em MB)
export function fetchMaxUploadSize() {
	return new Promise((resolve, reject) => {
		postData(
			'DOCUM_MultiUpload',
			'GetMaxUploadSize',
			{}, // parâmetros se necessário
			(data, response) => {
				const result = (response && response.data) || data;
				if (!result || typeof result.maxUploadSizeMB !== 'number') {
					reject(new Error('Invalid API response when fetching max upload size'));
				} else {
					resolve(result.maxUploadSizeMB);
				}
			},
			(err) => {
				console.error('Error fetching max upload size:', err);
				reject(err);
			}
		);
	});
}

