<template>
	<teleport
		v-if="formModalIsReady && showFormHeader"
		:to="`#${uiContainersId.header}`"
		:disabled="!isPopup || isNested">
		<div
			ref="formHeader"
			:class="{ 'c-sticky-header': isStickyHeader, 'sticky-top': isStickyTop }">
			<div
				v-if="showFormHeader"
				class="c-action-bar">
				<h1
					v-if="formControl.uiComponents.header && formInfo.designation"
					:id="formTitleId"
					class="form-header">
					{{ formInfo.designation }}
				</h1>

				<div class="c-action-bar__menu">
					<template
						v-for="(section, sectionId) in formButtonSections"
						:key="sectionId">
						<span
							v-if="showHeadingSep(sectionId)"
							class="main-title-sep" />

						<q-toggle-group
							v-if="formControl.uiComponents.headerButtons"
							borderless>
							<template
								v-for="btn in section"
								:key="btn.id">
								<q-toggle-group-item
									v-if="showFormHeaderButton(btn)"
									:model-value="btn.isSelected"
									:id="`top-${btn.id}`"
									:title="btn.text"
									:label="btn.label"
									:disabled="btn.disabled"
									@click="btn.action">
									<q-icon
										v-if="btn.icon"
										v-bind="btn.icon" />
								</q-toggle-group-item>
							</template>
						</q-toggle-group>
					</template>
				</div>
			</div>

			<q-anchor-container-horizontal
				v-if="$app.layout.FormAnchorsPosition === 'form-header' && visibleGroups.length > 0"
				:anchors="anchorGroups"
				:controls="visibleControls"
				@focus-control="(...args) => focusControl(...args)" />
		</div>
	</teleport>

	<teleport
		v-if="formModalIsReady && showFormBody"
		:to="`#${uiContainersId.body}`"
		:disabled="!isPopup || isNested">
		<q-validation-summary
			:messages="validationErrors"
			@error-clicked="focusField" />

		<div :class="[`float-${actionsPlacement}`, 'c-action-bar']">
			<q-button-group borderless>
				<template
					v-for="btn in formButtons"
					:key="btn.id">
					<q-button
						v-if="btn.isActive && btn.isVisible && btn.showInHeading"
						:id="`heading-${btn.id}`"
						:label="btn.text"
						:variant="btn.variant"
						:disabled="btn.disabled"
						:icon-pos="btn.iconPos"
						:class="btn.classes"
						@click="btn.action(); btn.emitAction ? $emit(btn.emitAction.name, btn.emitAction.params) : null">
						<q-icon
							v-if="btn.icon"
							v-bind="btn.icon" />
					</q-button>
				</template>
			</q-button-group>
		</div>

		<div
			class="form-flow"
			data-key="DSCPP"
			:data-loading="!formInitialDataLoaded">
			<template v-if="formControl.initialized && showFormBody">
				<q-row-container
					v-show="controls.DSCPP___PSEUDDESCRIPT.isVisible"
					is-large>
					<q-control-wrapper
						v-show="controls.DSCPP___PSEUDDESCRIPT.isVisible"
						class="row-line-group">
						<q-group-box-container
							id="DSCPP___PSEUDDESCRIPT"
							v-bind="controls.DSCPP___PSEUDDESCRIPT"
							:is-visible="controls.DSCPP___PSEUDDESCRIPT.isVisible">
							<!-- Start DSCPP___PSEUDDESCRIPT -->
							<q-row-container v-show="controls.DSCPP___DSCPPID______.isVisible || controls.DSCPP___DSCPPDATE____.isVisible || controls.DSCPP___DSCPPTITLE___.isVisible || controls.DSCPP___DSCPPCOMPLIES.isVisible">
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPID______.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPID______"
										v-on="controls.DSCPP___DSCPPID______.handlers"
										:loading="controls.DSCPP___DSCPPID______.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-numeric-input
											v-if="controls.DSCPP___DSCPPID______.isVisible"
											v-bind="controls.DSCPP___DSCPPID______.props"
											@update:model-value="model.ValId.fnUpdateValue" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPDATE____.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPDATE____"
										v-on="controls.DSCPP___DSCPPDATE____.handlers"
										:loading="controls.DSCPP___DSCPPDATE____.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-date-time-picker
											v-if="controls.DSCPP___DSCPPDATE____.isVisible"
											v-bind="controls.DSCPP___DSCPPDATE____.props"
											:model-value="model.ValDate.value"
											@reset-icon-click="model.ValDate.fnUpdateValue(model.ValDate.originalValue ?? new Date())"
											@update:model-value="model.ValDate.fnUpdateValue($event ?? '')" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPTITLE___.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPTITLE___"
										v-on="controls.DSCPP___DSCPPTITLE___.handlers"
										:loading="controls.DSCPP___DSCPPTITLE___.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPTITLE___.props"
											@blur="onBlur(controls.DSCPP___DSCPPTITLE___, model.ValTitle.value)"
											@change="model.ValTitle.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPCOMPLIES.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-checkbox"
										v-bind="controls.DSCPP___DSCPPCOMPLIES"
										v-on="controls.DSCPP___DSCPPCOMPLIES.handlers"
										:loading="controls.DSCPP___DSCPPCOMPLIES.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<template #label>
											<q-checkbox-input
												v-if="controls.DSCPP___DSCPPCOMPLIES.isVisible"
												v-bind="controls.DSCPP___DSCPPCOMPLIES.props"
												v-on="controls.DSCPP___DSCPPCOMPLIES.handlers" />
										</template>
									</base-input-structure>
								</q-control-wrapper>
							</q-row-container>
							<q-row-container v-show="controls.DSCPP___DSCPPCREATOR_.isVisible || controls.DSCPP___DSCPPSUBJECT_.isVisible">
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPCREATOR_.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPCREATOR_"
										v-on="controls.DSCPP___DSCPPCREATOR_.handlers"
										:loading="controls.DSCPP___DSCPPCREATOR_.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPCREATOR_.props"
											@blur="onBlur(controls.DSCPP___DSCPPCREATOR_, model.ValCreator.value)"
											@change="model.ValCreator.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPSUBJECT_.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPSUBJECT_"
										v-on="controls.DSCPP___DSCPPSUBJECT_.handlers"
										:loading="controls.DSCPP___DSCPPSUBJECT_.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPSUBJECT_.props"
											@blur="onBlur(controls.DSCPP___DSCPPSUBJECT_, model.ValSubject.value)"
											@change="model.ValSubject.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
							</q-row-container>
							<q-row-container v-show="controls.DSCPP___DSCPPPUBLISHE.isVisible || controls.DSCPP___DSCPPCONTRIBU.isVisible">
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPPUBLISHE.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPPUBLISHE"
										v-on="controls.DSCPP___DSCPPPUBLISHE.handlers"
										:loading="controls.DSCPP___DSCPPPUBLISHE.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPPUBLISHE.props"
											@blur="onBlur(controls.DSCPP___DSCPPPUBLISHE, model.ValPublisher.value)"
											@change="model.ValPublisher.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPCONTRIBU.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPCONTRIBU"
										v-on="controls.DSCPP___DSCPPCONTRIBU.handlers"
										:loading="controls.DSCPP___DSCPPCONTRIBU.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPCONTRIBU.props"
											@blur="onBlur(controls.DSCPP___DSCPPCONTRIBU, model.ValContributor.value)"
											@change="model.ValContributor.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
							</q-row-container>
							<q-row-container
								v-show="controls.DSCPP___DSCPPTYPE____.isVisible || controls.DSCPP___DSCPPFORMAT__.isVisible"
								is-large>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPTYPE____.isVisible"
									class="row-line-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPTYPE____"
										v-on="controls.DSCPP___DSCPPTYPE____.handlers"
										:loading="controls.DSCPP___DSCPPTYPE____.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPTYPE____.props"
											@blur="onBlur(controls.DSCPP___DSCPPTYPE____, model.ValType.value)"
											@change="model.ValType.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPFORMAT__.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPFORMAT__"
										v-on="controls.DSCPP___DSCPPFORMAT__.handlers"
										:loading="controls.DSCPP___DSCPPFORMAT__.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPFORMAT__.props"
											@blur="onBlur(controls.DSCPP___DSCPPFORMAT__, model.ValFormat.value)"
											@change="model.ValFormat.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
							</q-row-container>
							<q-row-container v-show="controls.DSCPP___DSCPPIDENTIFI.isVisible || controls.DSCPP___DSCPPSOURCE__.isVisible">
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPIDENTIFI.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPIDENTIFI"
										v-on="controls.DSCPP___DSCPPIDENTIFI.handlers"
										:loading="controls.DSCPP___DSCPPIDENTIFI.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPIDENTIFI.props"
											@blur="onBlur(controls.DSCPP___DSCPPIDENTIFI, model.ValIdentifier.value)"
											@change="model.ValIdentifier.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPSOURCE__.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPSOURCE__"
										v-on="controls.DSCPP___DSCPPSOURCE__.handlers"
										:loading="controls.DSCPP___DSCPPSOURCE__.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPSOURCE__.props"
											@blur="onBlur(controls.DSCPP___DSCPPSOURCE__, model.ValSource.value)"
											@change="model.ValSource.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
							</q-row-container>
							<q-row-container v-show="controls.DSCPP___DSCPPRELATION.isVisible || controls.DSCPP___DSCPPCOVERAGE.isVisible">
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPRELATION.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPRELATION"
										v-on="controls.DSCPP___DSCPPRELATION.handlers"
										:loading="controls.DSCPP___DSCPPRELATION.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPRELATION.props"
											@blur="onBlur(controls.DSCPP___DSCPPRELATION, model.ValRelation.value)"
											@change="model.ValRelation.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPCOVERAGE.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPCOVERAGE"
										v-on="controls.DSCPP___DSCPPCOVERAGE.handlers"
										:loading="controls.DSCPP___DSCPPCOVERAGE.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPCOVERAGE.props"
											@blur="onBlur(controls.DSCPP___DSCPPCOVERAGE, model.ValCoverage.value)"
											@change="model.ValCoverage.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
							</q-row-container>
							<q-row-container v-show="controls.DSCPP___LANGULANGUISO.isVisible || controls.DSCPP___COUNTCOUNTRY_.isVisible">
								<q-control-wrapper
									v-show="controls.DSCPP___LANGULANGUISO.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___LANGULANGUISO"
										v-on="controls.DSCPP___LANGULANGUISO.handlers"
										:loading="controls.DSCPP___LANGULANGUISO.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-lookup
											v-if="controls.DSCPP___LANGULANGUISO.isVisible"
											v-bind="controls.DSCPP___LANGULANGUISO.props"
											v-on="controls.DSCPP___LANGULANGUISO.handlers" />
										<q-see-more-dscpp-langulanguiso
											v-if="controls.DSCPP___LANGULANGUISO.seeMoreIsVisible"
											v-bind="controls.DSCPP___LANGULANGUISO.seeMoreParams"
											v-on="controls.DSCPP___LANGULANGUISO.handlers" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___COUNTCOUNTRY_.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___COUNTCOUNTRY_"
										v-on="controls.DSCPP___COUNTCOUNTRY_.handlers"
										:loading="controls.DSCPP___COUNTCOUNTRY_.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-lookup
											v-if="controls.DSCPP___COUNTCOUNTRY_.isVisible"
											v-bind="controls.DSCPP___COUNTCOUNTRY_.props"
											v-on="controls.DSCPP___COUNTCOUNTRY_.handlers" />
										<q-see-more-dscpp-countcountry
											v-if="controls.DSCPP___COUNTCOUNTRY_.seeMoreIsVisible"
											v-bind="controls.DSCPP___COUNTCOUNTRY_.seeMoreParams"
											v-on="controls.DSCPP___COUNTCOUNTRY_.handlers" />
									</base-input-structure>
								</q-control-wrapper>
							</q-row-container>
							<q-row-container v-show="controls.DSCPP___DSCPPRIGHTS__.isVisible">
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPRIGHTS__.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPRIGHTS__"
										v-on="controls.DSCPP___DSCPPRIGHTS__.handlers"
										:loading="controls.DSCPP___DSCPPRIGHTS__.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPRIGHTS__.props"
											@blur="onBlur(controls.DSCPP___DSCPPRIGHTS__, model.ValRights.value)"
											@change="model.ValRights.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
							</q-row-container>
							<!-- End DSCPP___PSEUDDESCRIPT -->
						</q-group-box-container>
					</q-control-wrapper>
				</q-row-container>
				<q-row-container
					v-show="controls.DSCPP___PSEUDZNIDENTI.isVisible"
					is-large>
					<q-control-wrapper
						v-show="controls.DSCPP___PSEUDZNIDENTI.isVisible"
						class="row-line-group">
						<q-group-collapsible
							id="DSCPP___PSEUDZNIDENTI"
							v-bind="controls.DSCPP___PSEUDZNIDENTI"
							v-on="controls.DSCPP___PSEUDZNIDENTI.handlers">
							<!-- Start DSCPP___PSEUDZNIDENTI -->
							<q-row-container v-show="controls.DSCPP___DSCPPISSUENUM.isVisible || controls.DSCPP___DSCPPINWORK__.isVisible">
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPISSUENUM.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPISSUENUM"
										v-on="controls.DSCPP___DSCPPISSUENUM.handlers"
										:loading="controls.DSCPP___DSCPPISSUENUM.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPISSUENUM.props"
											@blur="onBlur(controls.DSCPP___DSCPPISSUENUM, model.ValIssuenumber.value)"
											@change="model.ValIssuenumber.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPINWORK__.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPINWORK__"
										v-on="controls.DSCPP___DSCPPINWORK__.handlers"
										:loading="controls.DSCPP___DSCPPINWORK__.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPINWORK__.props"
											@blur="onBlur(controls.DSCPP___DSCPPINWORK__, model.ValInwork.value)"
											@change="model.ValInwork.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
							</q-row-container>
							<q-row-container v-show="controls.DSCPP___DSCPPEXTEPROD.isVisible || controls.DSCPP___DSCPPEXTECODE.isVisible">
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPEXTEPROD.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPEXTEPROD"
										v-on="controls.DSCPP___DSCPPEXTEPROD.handlers"
										:loading="controls.DSCPP___DSCPPEXTEPROD.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPEXTEPROD.props"
											@blur="onBlur(controls.DSCPP___DSCPPEXTEPROD, model.ValExtensionproducer.value)"
											@change="model.ValExtensionproducer.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPEXTECODE.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPEXTECODE"
										v-on="controls.DSCPP___DSCPPEXTECODE.handlers"
										:loading="controls.DSCPP___DSCPPEXTECODE.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPEXTECODE.props"
											@blur="onBlur(controls.DSCPP___DSCPPEXTECODE, model.ValExtensioncode.value)"
											@change="model.ValExtensioncode.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
							</q-row-container>
							<q-row-container v-show="controls.DSCPP___DSCPPMODICODE.isVisible || controls.DSCPP___DSCPPSYSTEMDI.isVisible || controls.DSCPP___DMCDRCOMPLCOD.isVisible || controls.DSCPP___DSCPPLEARNCOD.isVisible || controls.DSCPP___DSCPPLEAREVCD.isVisible || controls.DSCPP___DSCPPTECHNAME.isVisible || controls.DSCPP___DSCPPINFONAME.isVisible || controls.DSCPP___DSCPPISSUETYP.isVisible">
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPMODICODE.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPMODICODE"
										v-on="controls.DSCPP___DSCPPMODICODE.handlers"
										:loading="controls.DSCPP___DSCPPMODICODE.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPMODICODE.props"
											@blur="onBlur(controls.DSCPP___DSCPPMODICODE, model.ValModelidentcode.value)"
											@change="model.ValModelidentcode.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPSYSTEMDI.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPSYSTEMDI"
										v-on="controls.DSCPP___DSCPPSYSTEMDI.handlers"
										:loading="controls.DSCPP___DSCPPSYSTEMDI.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPSYSTEMDI.props"
											@blur="onBlur(controls.DSCPP___DSCPPSYSTEMDI, model.ValSystemdiffcode.value)"
											@change="model.ValSystemdiffcode.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DMCDRCOMPLCOD.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DMCDRCOMPLCOD"
										v-on="controls.DSCPP___DMCDRCOMPLCOD.handlers"
										:loading="controls.DSCPP___DMCDRCOMPLCOD.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-lookup
											v-if="controls.DSCPP___DMCDRCOMPLCOD.isVisible"
											v-bind="controls.DSCPP___DMCDRCOMPLCOD.props"
											v-on="controls.DSCPP___DMCDRCOMPLCOD.handlers" />
										<q-see-more-dscpp-dmcdrcomplcod
											v-if="controls.DSCPP___DMCDRCOMPLCOD.seeMoreIsVisible"
											v-bind="controls.DSCPP___DMCDRCOMPLCOD.seeMoreParams"
											v-on="controls.DSCPP___DMCDRCOMPLCOD.handlers" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPLEARNCOD.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPLEARNCOD"
										v-on="controls.DSCPP___DSCPPLEARNCOD.handlers"
										:loading="controls.DSCPP___DSCPPLEARNCOD.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPLEARNCOD.props"
											@blur="onBlur(controls.DSCPP___DSCPPLEARNCOD, model.ValLearncode.value)"
											@change="model.ValLearncode.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPLEAREVCD.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPLEAREVCD"
										v-on="controls.DSCPP___DSCPPLEAREVCD.handlers"
										:loading="controls.DSCPP___DSCPPLEAREVCD.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPLEAREVCD.props"
											@blur="onBlur(controls.DSCPP___DSCPPLEAREVCD, model.ValLearneventcode.value)"
											@change="model.ValLearneventcode.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPTECHNAME.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPTECHNAME"
										v-on="controls.DSCPP___DSCPPTECHNAME.handlers"
										:loading="controls.DSCPP___DSCPPTECHNAME.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPTECHNAME.props"
											@blur="onBlur(controls.DSCPP___DSCPPTECHNAME, model.ValTechname.value)"
											@change="model.ValTechname.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPINFONAME.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPINFONAME"
										v-on="controls.DSCPP___DSCPPINFONAME.handlers"
										:loading="controls.DSCPP___DSCPPINFONAME.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPINFONAME.props"
											@blur="onBlur(controls.DSCPP___DSCPPINFONAME, model.ValInfoname.value)"
											@change="model.ValInfoname.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPISSUETYP.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPISSUETYP"
										v-on="controls.DSCPP___DSCPPISSUETYP.handlers"
										:loading="controls.DSCPP___DSCPPISSUETYP.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-select
											v-if="controls.DSCPP___DSCPPISSUETYP.isVisible"
											v-bind="controls.DSCPP___DSCPPISSUETYP.props"
											@update:model-value="model.ValIssuetype.fnUpdateValue" />
									</base-input-structure>
								</q-control-wrapper>
							</q-row-container>
							<!-- End DSCPP___PSEUDZNIDENTI -->
						</q-group-collapsible>
					</q-control-wrapper>
				</q-row-container>
				<q-row-container
					v-show="controls.DSCPP___PSEUDZNDOCUM_.isVisible"
					is-large>
					<q-control-wrapper
						v-show="controls.DSCPP___PSEUDZNDOCUM_.isVisible"
						class="row-line-group">
						<q-group-collapsible
							id="DSCPP___PSEUDZNDOCUM_"
							v-bind="controls.DSCPP___PSEUDZNDOCUM_"
							v-on="controls.DSCPP___PSEUDZNDOCUM_.handlers">
							<!-- Start DSCPP___PSEUDZNDOCUM_ -->
							<q-row-container
								v-show="controls.DSCPP___PSEUDFICWEB1_.isVisible"
								is-large>
								<q-control-wrapper
									v-show="controls.DSCPP___PSEUDFICWEB1_.isVisible"
									class="row-line-group">
									<q-table
										v-show="controls.DSCPP___PSEUDFICWEB1_.isVisible"
										v-bind="controls.DSCPP___PSEUDFICWEB1_"
										v-on="{
											...controls.DSCPP___PSEUDFICWEB1_.handlers,
											'row-action': handleTableRowAction
										}"
										/>
									<q-table-extra-extension
										:list-ctrl="controls.DSCPP___PSEUDFICWEB1_"
										v-on="controls.DSCPP___PSEUDFICWEB1_.handlers" />
								</q-control-wrapper>
							</q-row-container>
							<!-- End DSCPP___PSEUDZNDOCUM_ -->
						</q-group-collapsible>
					</q-control-wrapper>
				</q-row-container>
				<q-row-container
					v-show="controls.DSCPP___PSEUDNOVOGR01.isVisible"
					is-large>
					<q-control-wrapper
						v-show="controls.DSCPP___PSEUDNOVOGR01.isVisible"
						class="row-line-group">
						<q-group-collapsible
							id="DSCPP___PSEUDNOVOGR01"
							v-bind="controls.DSCPP___PSEUDNOVOGR01"
							v-on="controls.DSCPP___PSEUDNOVOGR01.handlers">
							<!-- Start DSCPP___PSEUDNOVOGR01 -->
							<q-row-container v-show="controls.DSCPP___DSCPPSECURCLA.isVisible || controls.DSCPP___DSCPPSECCOCLA.isVisible || controls.DSCPP___DSCPPSECCAVAT.isVisible">
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPSECURCLA.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPSECURCLA"
										v-on="controls.DSCPP___DSCPPSECURCLA.handlers"
										:loading="controls.DSCPP___DSCPPSECURCLA.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-select
											v-if="controls.DSCPP___DSCPPSECURCLA.isVisible"
											v-bind="controls.DSCPP___DSCPPSECURCLA.props"
											@update:model-value="model.ValSecurcla.fnUpdateValue" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPSECCOCLA.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPSECCOCLA"
										v-on="controls.DSCPP___DSCPPSECCOCLA.handlers"
										:loading="controls.DSCPP___DSCPPSECCOCLA.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-select
											v-if="controls.DSCPP___DSCPPSECCOCLA.isVisible"
											v-bind="controls.DSCPP___DSCPPSECCOCLA.props"
											@update:model-value="model.ValSeccocla.fnUpdateValue" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPSECCAVAT.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPSECCAVAT"
										v-on="controls.DSCPP___DSCPPSECCAVAT.handlers"
										:loading="controls.DSCPP___DSCPPSECCAVAT.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-select
											v-if="controls.DSCPP___DSCPPSECCAVAT.isVisible"
											v-bind="controls.DSCPP___DSCPPSECCAVAT.props"
											@update:model-value="model.ValSeccavat.fnUpdateValue" />
									</base-input-structure>
								</q-control-wrapper>
							</q-row-container>
							<!-- End DSCPP___PSEUDNOVOGR01 -->
						</q-group-collapsible>
					</q-control-wrapper>
				</q-row-container>
				<q-row-container
					v-show="controls.DSCPP___PSEUDSTATUS__.isVisible"
					is-large>
					<q-control-wrapper
						v-show="controls.DSCPP___PSEUDSTATUS__.isVisible"
						class="row-line-group">
						<q-group-collapsible
							id="DSCPP___PSEUDSTATUS__"
							v-bind="controls.DSCPP___PSEUDSTATUS__"
							v-on="controls.DSCPP___PSEUDSTATUS__.handlers">
							<!-- Start DSCPP___PSEUDSTATUS__ -->
							<q-row-container v-show="controls.DSCPP___DSCPPDATADIST.isVisible || controls.DSCPP___DSCPPEXPREGST.isVisible || controls.DSCPP___DSCPPDATAHAND.isVisible || controls.DSCPP___DSCPPDATADEST.isVisible || controls.DSCPP___DSCPPDATADISC.isVisible">
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPDATADIST.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPDATADIST"
										v-on="controls.DSCPP___DSCPPDATADIST.handlers"
										:loading="controls.DSCPP___DSCPPDATADIST.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPDATADIST.props"
											@blur="onBlur(controls.DSCPP___DSCPPDATADIST, model.ValDatadistribution.value)"
											@change="model.ValDatadistribution.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPEXPREGST.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-textarea"
										v-bind="controls.DSCPP___DSCPPEXPREGST"
										v-on="controls.DSCPP___DSCPPEXPREGST.handlers"
										:loading="controls.DSCPP___DSCPPEXPREGST.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-area
											v-if="controls.DSCPP___DSCPPEXPREGST.isVisible"
											v-bind="controls.DSCPP___DSCPPEXPREGST.props"
											v-on="controls.DSCPP___DSCPPEXPREGST.handlers" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPDATAHAND.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPDATAHAND"
										v-on="controls.DSCPP___DSCPPDATAHAND.handlers"
										:loading="controls.DSCPP___DSCPPDATAHAND.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPDATAHAND.props"
											@blur="onBlur(controls.DSCPP___DSCPPDATAHAND, model.ValDatahandling.value)"
											@change="model.ValDatahandling.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPDATADEST.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPDATADEST"
										v-on="controls.DSCPP___DSCPPDATADEST.handlers"
										:loading="controls.DSCPP___DSCPPDATADEST.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPDATADEST.props"
											@blur="onBlur(controls.DSCPP___DSCPPDATADEST, model.ValDatadestruction.value)"
											@change="model.ValDatadestruction.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPDATADISC.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPDATADISC"
										v-on="controls.DSCPP___DSCPPDATADISC.handlers"
										:loading="controls.DSCPP___DSCPPDATADISC.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPDATADISC.props"
											@blur="onBlur(controls.DSCPP___DSCPPDATADISC, model.ValDatadisclosure.value)"
											@change="model.ValDatadisclosure.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
							</q-row-container>
							<q-row-container
								v-show="controls.DSCPP___DSCPPCOPYRIGH.isVisible || controls.DSCPP___DSCPPPOLICYST.isVisible || controls.DSCPP___DSCPPDATACOND.isVisible || controls.DSCPP___COMPRNAME____.isVisible || controls.DSCPP___COMPONAME____.isVisible"
								is-large>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPCOPYRIGH.isVisible"
									class="row-line-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPCOPYRIGH"
										v-on="controls.DSCPP___DSCPPCOPYRIGH.handlers"
										:loading="controls.DSCPP___DSCPPCOPYRIGH.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-editor
											v-if="controls.DSCPP___DSCPPCOPYRIGH.isVisible"
											v-bind="controls.DSCPP___DSCPPCOPYRIGH.props"
											v-on="controls.DSCPP___DSCPPCOPYRIGH.handlers" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPPOLICYST.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPPOLICYST"
										v-on="controls.DSCPP___DSCPPPOLICYST.handlers"
										:loading="controls.DSCPP___DSCPPPOLICYST.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPPOLICYST.props"
											@blur="onBlur(controls.DSCPP___DSCPPPOLICYST, model.ValPolicystatement.value)"
											@change="model.ValPolicystatement.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___DSCPPDATACOND.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___DSCPPDATACOND"
										v-on="controls.DSCPP___DSCPPDATACOND.handlers"
										:loading="controls.DSCPP___DSCPPDATACOND.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-text-field
											v-bind="controls.DSCPP___DSCPPDATACOND.props"
											@blur="onBlur(controls.DSCPP___DSCPPDATACOND, model.ValDataconds.value)"
											@change="model.ValDataconds.fnUpdateValueOnChange" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___COMPRNAME____.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___COMPRNAME____"
										v-on="controls.DSCPP___COMPRNAME____.handlers"
										:loading="controls.DSCPP___COMPRNAME____.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-lookup
											v-if="controls.DSCPP___COMPRNAME____.isVisible"
											v-bind="controls.DSCPP___COMPRNAME____.props"
											v-on="controls.DSCPP___COMPRNAME____.handlers" />
										<q-see-more-dscpp-comprname
											v-if="controls.DSCPP___COMPRNAME____.seeMoreIsVisible"
											v-bind="controls.DSCPP___COMPRNAME____.seeMoreParams"
											v-on="controls.DSCPP___COMPRNAME____.handlers" />
									</base-input-structure>
								</q-control-wrapper>
								<q-control-wrapper
									v-show="controls.DSCPP___COMPONAME____.isVisible"
									class="control-join-group">
									<base-input-structure
										class="i-text"
										v-bind="controls.DSCPP___COMPONAME____"
										v-on="controls.DSCPP___COMPONAME____.handlers"
										:loading="controls.DSCPP___COMPONAME____.props.loading"
										:reporting-mode-on="reportingModeCAV"
										:suggestion-mode-on="suggestionModeOn">
										<q-lookup
											v-if="controls.DSCPP___COMPONAME____.isVisible"
											v-bind="controls.DSCPP___COMPONAME____.props"
											v-on="controls.DSCPP___COMPONAME____.handlers" />
										<q-see-more-dscpp-componame
											v-if="controls.DSCPP___COMPONAME____.seeMoreIsVisible"
											v-bind="controls.DSCPP___COMPONAME____.seeMoreParams"
											v-on="controls.DSCPP___COMPONAME____.handlers" />
									</base-input-structure>
								</q-control-wrapper>
							</q-row-container>
							<q-row-container
								v-show="controls.DSCPP___PSEUDCOMPONEN.isVisible"
								is-large>
								<q-control-wrapper
									v-show="controls.DSCPP___PSEUDCOMPONEN.isVisible"
									class="row-line-group">
									<q-table
										v-show="controls.DSCPP___PSEUDCOMPONEN.isVisible"
										v-bind="controls.DSCPP___PSEUDCOMPONEN"
										v-on="controls.DSCPP___PSEUDCOMPONEN.handlers" />
									<q-table-extra-extension
										:list-ctrl="controls.DSCPP___PSEUDCOMPONEN"
										v-on="controls.DSCPP___PSEUDCOMPONEN.handlers" />
								</q-control-wrapper>
							</q-row-container>
							<!-- End DSCPP___PSEUDSTATUS__ -->
						</q-group-collapsible>
					</q-control-wrapper>
				</q-row-container>
				<q-row-container
					v-show="controls.DSCPP___PSEUDAPPLICAB.isVisible"
					is-large>
					<q-control-wrapper
						v-show="controls.DSCPP___PSEUDAPPLICAB.isVisible"
						class="row-line-group">
						<q-group-collapsible
							id="DSCPP___PSEUDAPPLICAB"
							v-bind="controls.DSCPP___PSEUDAPPLICAB"
							v-on="controls.DSCPP___PSEUDAPPLICAB.handlers">
							<!-- Start DSCPP___PSEUDAPPLICAB -->
							<q-row-container
								v-show="controls.DSCPP___PSEUDAPPS____.isVisible"
								is-large>
								<q-control-wrapper
									v-show="controls.DSCPP___PSEUDAPPS____.isVisible"
									class="row-line-group">
									<q-table
										v-show="controls.DSCPP___PSEUDAPPS____.isVisible"
										v-bind="controls.DSCPP___PSEUDAPPS____"
										v-on="controls.DSCPP___PSEUDAPPS____.handlers" />
									<q-table-extra-extension
										:list-ctrl="controls.DSCPP___PSEUDAPPS____"
										v-on="controls.DSCPP___PSEUDAPPS____.handlers" />
								</q-control-wrapper>
							</q-row-container>
							<!-- End DSCPP___PSEUDAPPLICAB -->
						</q-group-collapsible>
					</q-control-wrapper>
				</q-row-container>
				<q-row-container
					v-show="controls.DSCPP___PSEUDVERSIONS.isVisible"
					is-large>
					<q-control-wrapper
						v-show="controls.DSCPP___PSEUDVERSIONS.isVisible"
						class="row-line-group">
						<q-group-collapsible
							id="DSCPP___PSEUDVERSIONS"
							v-bind="controls.DSCPP___PSEUDVERSIONS"
							v-on="controls.DSCPP___PSEUDVERSIONS.handlers">
							<!-- Start DSCPP___PSEUDVERSIONS -->
							<q-row-container
								v-show="controls.DSCPP___PSEUDEXPVRSN_.isVisible"
								is-large>
								<q-control-wrapper
									v-show="controls.DSCPP___PSEUDEXPVRSN_.isVisible"
									class="row-line-group">
									<q-table
										v-show="controls.DSCPP___PSEUDEXPVRSN_.isVisible"
										v-bind="controls.DSCPP___PSEUDEXPVRSN_"
										v-on="controls.DSCPP___PSEUDEXPVRSN_.handlers" />
									<q-table-extra-extension
										:list-ctrl="controls.DSCPP___PSEUDEXPVRSN_"
										v-on="controls.DSCPP___PSEUDEXPVRSN_.handlers" />
								</q-control-wrapper>
							</q-row-container>
							<!-- End DSCPP___PSEUDVERSIONS -->
						</q-group-collapsible>
					</q-control-wrapper>
				</q-row-container>
				<q-row-container
					v-show="controls.DSCPP___PSEUDZNIETDD_.isVisible"
					is-large>
					<q-control-wrapper
						v-show="controls.DSCPP___PSEUDZNIETDD_.isVisible"
						class="row-line-group">
						<q-group-collapsible
							id="DSCPP___PSEUDZNIETDD_"
							v-bind="controls.DSCPP___PSEUDZNIETDD_"
							v-on="controls.DSCPP___PSEUDZNIETDD_.handlers">
							<!-- Start DSCPP___PSEUDZNIETDD_ -->
							<q-row-container
								v-show="controls.DSCPP___PSEUDLSTIETDD.isVisible"
								is-large>
								<q-control-wrapper
									v-show="controls.DSCPP___PSEUDLSTIETDD.isVisible"
									class="row-line-group">
									<q-table
										v-show="controls.DSCPP___PSEUDLSTIETDD.isVisible"
										v-bind="controls.DSCPP___PSEUDLSTIETDD"
										v-on="controls.DSCPP___PSEUDLSTIETDD.handlers" />
									<q-table-extra-extension
										:list-ctrl="controls.DSCPP___PSEUDLSTIETDD"
										v-on="controls.DSCPP___PSEUDLSTIETDD.handlers" />
								</q-control-wrapper>
							</q-row-container>
							<!-- End DSCPP___PSEUDZNIETDD_ -->
						</q-group-collapsible>
					</q-control-wrapper>
				</q-row-container>
			</template>
		</div>
	</teleport>

	<hr v-if="!isPopup && showFormFooter" />

	<teleport
		v-if="formModalIsReady && showFormFooter"
		:to="`#${uiContainersId.footer}`"
		:disabled="!isPopup || isNested">
		<q-row-container v-if="showFormFooter">
			<div id="footer-action-btns">
				<template
					v-for="btn in formButtons"
					:key="btn.id">
					<q-button
						v-if="btn.isActive && btn.isVisible && btn.showInFooter"
						:id="`bottom-${btn.id}`"
						:label="btn.text"
						:variant="btn.variant"
						:disabled="btn.disabled"
						:icon-pos="btn.iconPos"
						:class="btn.classes"
						@click="btn.action(); btn.emitAction ? $emit(btn.emitAction.name, btn.emitAction.params) : null">
						<q-icon
							v-if="btn.icon"
							v-bind="btn.icon" />
					</q-button>
				</template>
			</div>
		</q-row-container>
	</teleport>
	<template v-if="showMultiUploadModal">
		<QMultiFileUploadPanel :visible="showMultiUploadModal"
							   @close="showMultiUploadModal = false"
							   @uploaded="onMultiFilesUploaded"
							   :parent-id="model.ValCoddscrp.value"
							   table="DOCUM"
							   parent-table="DSCPP" />
	</template>
</template>

<script>
	/* eslint-disable no-unused-vars */
	import { computed, defineAsyncComponent, readonly } from 'vue'
	import { useRoute } from 'vue-router'

	import FormHandlers from '@/mixins/formHandlers.js'
	import formFunctions from '@/mixins/formFunctions.js'
	import genericFunctions from '@quidgest/clientapp/utils/genericFunctions'
	import listFunctions from '@/mixins/listFunctions.js'
	import listColumnTypes from '@/mixins/listColumnTypes.js'
	import modelFieldType from '@quidgest/clientapp/models/fields'
	import fieldControlClass from '@/mixins/fieldControl.js'
	import qEnums from '@quidgest/clientapp/constants/enums'
	import { resetProgressBar, setProgressBar } from '@/utils/layout.js'

	import hardcodedTexts from '@/hardcodedTexts.js'
	import netAPI from '@quidgest/clientapp/network'
	import asyncProcM from '@quidgest/clientapp/composables/async'
	import qApi from '@/api/genio/quidgestFunctions.js'
	import qFunctions from '@/api/genio/projectFunctions.js'
	import qProjArrays from '@/api/genio/projectArrays.js'
	/* eslint-enable no-unused-vars */

	import FormViewModel from './QFormDscppViewModel.js'

	const requiredTextResources = ['QFormDscpp', 'hardcoded', 'messages']

/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS FORM_INCLUDEJS DSCPP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */

	export default {
		name: 'QFormDscpp',

		components: {
            QMultiFileUploadPanel: defineAsyncComponent(() => import('@/components/QMultiFileUploadPanel.vue')),
			QSeeMoreDscppLangulanguiso: defineAsyncComponent(() => import('@/views/forms/FormDscpp/dbedits/DscppLangulanguisoSeeMore.vue')),
			QSeeMoreDscppCountcountry: defineAsyncComponent(() => import('@/views/forms/FormDscpp/dbedits/DscppCountcountrySeeMore.vue')),
			QSeeMoreDscppDmcdrcomplcod: defineAsyncComponent(() => import('@/views/forms/FormDscpp/dbedits/DscppDmcdrcomplcodSeeMore.vue')),
			QSeeMoreDscppComprname: defineAsyncComponent(() => import('@/views/forms/FormDscpp/dbedits/DscppComprnameSeeMore.vue')),
			QSeeMoreDscppComponame: defineAsyncComponent(() => import('@/views/forms/FormDscpp/dbedits/DscppComponameSeeMore.vue')),
		},

		mixins: [
			FormHandlers
		],

		props: {
			/**
			 * Parameters passed in case the form is nested.
			 */
			nestedRouteParams: {
				type: Object,
				default: () => ({
					name: 'DSCPP',
					location: 'form-DSCPP',
					params: {
						isNested: true
					}
				})
			}
		},

		expose: [
			'cancel',
			'initFormProperties',
			'navigationId'
		],

		setup(props)
		{
			const route = useRoute()

			return {
				/*
				 * As properties are reactive, when using $route.params, then when we exit it updates cached components.
				 * Properties have no value and this creates an error in new versions of vue-router.
				 * That's why the value has to be copied to a local property to be used in the router-link tag.
				 */
				currentRouteParams: props.isNested ? {} : route.params
			}
		},

		data()
		{
			// eslint-disable-next-line
			const vm = this
			return {
				componentOnLoadProc: asyncProcM.getProcListMonitor('QFormLDscpp', false),

				showMultiUploadModal: false, // Controla a visibilidade do modal de upload múltiplo

				interfaceMetadata: {
					id: 'QFormDscpp', // Used for resources
					requiredTextResources
				},

				formInfo: {
					type: 'normal',
					name: 'DSCPP',
					route: 'form-DSCPP',
					area: 'DSCPP',
					primaryKey: 'ValCoddscrp',
					designation: computed(() => this.Resources.DESCRIPTIVE47658),
					identifier: '', // Unique identifier received by route (when it's nested).
					mode: ''
				},

				formButtons: {
					changeToShow: {
						id: 'change-to-show-btn',
						icon: {
							icon: 'view',
							type: 'svg'
						},
						type: 'form-mode',
						text: computed(() => vm.Resources[hardcodedTexts.view]),
						showInHeader: true,
						showInFooter: false,
						isActive: false,
						isSelected: computed(() => vm.formModes.show === vm.formInfo.mode),
						isVisible: computed(() => vm.authData.isAllowed && [vm.formModes.show, vm.formModes.edit, vm.formModes.delete].includes(vm.formInfo.mode)),
						action: vm.changeToShowMode
					},
					changeToEdit: {
						id: 'change-to-edit-btn',
						icon: {
							icon: 'pencil',
							type: 'svg'
						},
						type: 'form-mode',
						text: computed(() => vm.Resources[hardcodedTexts.edit]),
						showInHeader: true,
						showInFooter: false,
						isActive: false,
						isSelected: computed(() => vm.formModes.edit === vm.formInfo.mode),
						isVisible: computed(() => vm.authData.isAllowed && [vm.formModes.show, vm.formModes.edit, vm.formModes.delete].includes(vm.formInfo.mode)),
						action: vm.changeToEditMode
					},
					changeToDuplicate: {
						id: 'change-to-dup-btn',
						icon: {
							icon: 'duplicate',
							type: 'svg'
						},
						type: 'form-mode',
						text: computed(() => vm.Resources[hardcodedTexts.duplicate]),
						showInHeader: true,
						showInFooter: false,
						isActive: false,
						isSelected: computed(() => vm.formModes.duplicate === vm.formInfo.mode),
						isVisible: computed(() => vm.authData.isAllowed && vm.formModes.new !== vm.formInfo.mode),
						action: vm.changeToDupMode
					},
					changeToDelete: {
						id: 'change-to-delete-btn',
						icon: {
							icon: 'delete',
							type: 'svg'
						},
						type: 'form-mode',
						text: computed(() => vm.Resources[hardcodedTexts.delete]),
						showInHeader: true,
						showInFooter: false,
						isActive: false,
						isSelected: computed(() => vm.formModes.delete === vm.formInfo.mode),
						isVisible: computed(() => vm.authData.isAllowed && [vm.formModes.show, vm.formModes.edit, vm.formModes.delete].includes(vm.formInfo.mode)),
						action: vm.changeToDeleteMode
					},
					changeToInsert: {
						id: 'change-to-insert-btn',
						icon: {
							icon: 'add',
							type: 'svg'
						},
						type: 'form-insert',
						text: computed(() => vm.Resources[hardcodedTexts.insert]),
						label: computed(() => vm.Resources[hardcodedTexts.insert]),
						showInHeader: true,
						showInFooter: false,
						isActive: false,
						isSelected: computed(() => vm.formModes.new === vm.formInfo.mode),
						isVisible: computed(() => vm.authData.isAllowed && vm.formModes.duplicate !== vm.formInfo.mode),
						action: vm.changeToInsertMode
					},
					repeatInsertBtn: {
						id: 'repeat-insert-btn',
						icon: {
							icon: 'save-new',
							type: 'svg'
						},
						type: 'form-action',
						text: computed(() => vm.Resources[hardcodedTexts.repeatInsert]),
						variant: 'bold',
						showInHeader: true,
						showInFooter: true,
						isActive: false,
						isVisible: computed(() => vm.authData.isAllowed && vm.formInfo.mode === vm.formModes.new),
						action: () => vm.saveForm(true)
					},
					saveBtn: {
						id: 'save-btn',
						icon: {
							icon: 'save',
							type: 'svg'
						},
						type: 'form-action',
						text: computed(() => vm.Resources.GRAVAR45301),
						variant: 'bold',
						showInHeader: true,
						showInFooter: true,
						isActive: true,
						isVisible: computed(() => vm.authData.isAllowed && vm.isEditable),
						action: vm.saveForm
					},
					confirmBtn: {
						id: 'confirm-btn',
						icon: {
							icon: 'check',
							type: 'svg'
						},
						type: 'form-action',
						text: computed(() => vm.Resources[vm.isNested ? hardcodedTexts.delete : hardcodedTexts.confirm]),
						variant: 'bold',
						showInHeader: true,
						showInFooter: true,
						isActive: true,
						isVisible: computed(() => vm.authData.isAllowed && (vm.formInfo.mode === vm.formModes.delete || vm.isNested)),
						action: vm.deleteRecord
					},
					cancelBtn: {
						id: 'cancel-btn',
						icon: {
							icon: 'cancel',
							type: 'svg'
						},
						type: 'form-action',
						text: computed(() => vm.Resources.CANCELAR49513),
						showInHeader: true,
						showInFooter: true,
						isActive: true,
						isVisible: computed(() => vm.authData.isAllowed && vm.isEditable),
						action: vm.leaveForm
					},
					resetCancelBtn: {
						id: 'reset-cancel-btn',
						icon: {
							icon: 'cancel',
							type: 'svg'
						},
						type: 'form-action',
						text: computed(() => vm.Resources[hardcodedTexts.cancel]),
						showInHeader: true,
						showInFooter: true,
						isActive: false,
						isVisible: computed(() => vm.authData.isAllowed && vm.isEditable),
						action: () => vm.model.resetValues(),
						emitAction: {
							name: 'deselect',
							params: {}
						}
					},
					editBtn: {
						id: 'edit-btn',
						icon: {
							icon: 'pencil',
							type: 'svg'
						},
						type: 'form-action',
						text: computed(() => vm.Resources[hardcodedTexts.edit]),
						variant: 'bold',
						showInHeader: true,
						showInFooter: false,
						isActive: false,
						isVisible: computed(() => vm.authData.isAllowed && vm.parentFormMode !== vm.formModes.show && vm.parentFormMode !== vm.formModes.delete),
						action: () => {},
						emitAction: {
							name: 'edit',
							params: {}
						}
					},
					deleteQuickBtn: {
						id: 'delete-btn',
						icon: {
							icon: 'bin',
							type: 'svg'
						},
						type: 'form-action',
						text: computed(() => vm.Resources[hardcodedTexts.delete]),
						variant: 'bold',
						showInHeader: true,
						showInFooter: false,
						isActive: false,
						isVisible: computed(() => vm.authData.isAllowed && vm.parentFormMode !== vm.formModes.show && (typeof vm.permissions.canDelete === 'boolean' ? vm.permissions.canDelete : true)),
						action: vm.deleteRecord
					},
					backBtn: {
						id: 'back-btn',
						icon: {
							icon: 'back',
							type: 'svg'
						},
						type: 'form-action',
						text: computed(() => vm.isPopup ? vm.Resources[hardcodedTexts.close] : vm.Resources[hardcodedTexts.goBack]),
						showInHeader: true,
						showInFooter: true,
						isActive: true,
						isVisible: computed(() => !vm.authData.isAllowed || !vm.isEditable),
						action: vm.leaveForm
					}
				},

				controls: {
					DSCPP___PSEUDDESCRIPT: new fieldControlClass.GroupControl({
						id: 'DSCPP___PSEUDDESCRIPT',
						name: 'DESCRIPT',
						size: 'block',
						label: computed(() => this.Resources.DESCRIPTION07438),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						isCollapsible: false,
						anchored: false,
						directChildren: ['DSCPP___DSCPPID______', 'DSCPP___DSCPPDATE____', 'DSCPP___DSCPPTITLE___', 'DSCPP___DSCPPCOMPLIES', 'DSCPP___DSCPPCREATOR_', 'DSCPP___DSCPPSUBJECT_', 'DSCPP___DSCPPPUBLISHE', 'DSCPP___DSCPPCONTRIBU', 'DSCPP___DSCPPTYPE____', 'DSCPP___DSCPPFORMAT__', 'DSCPP___DSCPPIDENTIFI', 'DSCPP___DSCPPSOURCE__', 'DSCPP___DSCPPRELATION', 'DSCPP___DSCPPCOVERAGE', 'DSCPP___LANGULANGUISO', 'DSCPP___COUNTCOUNTRY_', 'DSCPP___DSCPPRIGHTS__'],
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPID______: new fieldControlClass.NumberControl({
						modelField: 'ValId',
						valueChangeEvent: 'fieldChange:dscpp.id',
						id: 'DSCPP___DSCPPID______',
						name: 'ID',
						size: 'mini',
						label: computed(() => this.Resources.ID36840),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDDESCRIPT',
						maxIntegers: 6,
						maxDecimals: 0,
						isSequencial: true,
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPDATE____: new fieldControlClass.DateControl({
						modelField: 'ValDate',
						valueChangeEvent: 'fieldChange:dscpp.date',
						id: 'DSCPP___DSCPPDATE____',
						name: 'DATE',
						size: 'small',
						label: computed(() => this.Resources.DATE18475),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDDESCRIPT',
						format: 'date',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPTITLE___: new fieldControlClass.StringControl({
						modelField: 'ValTitle',
						valueChangeEvent: 'fieldChange:dscpp.title',
						id: 'DSCPP___DSCPPTITLE___',
						name: 'TITLE',
						size: 'xxlarge',
						label: computed(() => this.Resources.TITLE21885),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDDESCRIPT',
						maxLength: 150,
						labelId: 'label_DSCPP___DSCPPTITLE___',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPCOMPLIES: new fieldControlClass.BooleanControl({
						modelField: 'ValComplies',
						valueChangeEvent: 'fieldChange:dscpp.complies',
						id: 'DSCPP___DSCPPCOMPLIES',
						name: 'COMPLIES',
						size: 'medium',
						label: computed(() => this.Resources.S1000D_COMPLIANCE36736),
						placeholder: '',
						labelPosition: computed(() => this.$app.layout.CheckboxLabelAlignment),
						container: 'DSCPP___PSEUDDESCRIPT',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: 1==1
								return 1===1
							},
							dependencyEvents: [],
							isServerRecalc: false,
						},
					}, this),
					DSCPP___DSCPPCREATOR_: new fieldControlClass.StringControl({
						modelField: 'ValCreator',
						valueChangeEvent: 'fieldChange:dscpp.creator',
						id: 'DSCPP___DSCPPCREATOR_',
						name: 'CREATOR',
						size: 'xxlarge',
						label: computed(() => this.Resources.CREATOR00370),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDDESCRIPT',
						maxLength: 150,
						labelId: 'label_DSCPP___DSCPPCREATOR_',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPSUBJECT_: new fieldControlClass.StringControl({
						modelField: 'ValSubject',
						valueChangeEvent: 'fieldChange:dscpp.subject',
						id: 'DSCPP___DSCPPSUBJECT_',
						name: 'SUBJECT',
						size: 'xxlarge',
						label: computed(() => this.Resources.SUBJECT33942),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDDESCRIPT',
						maxLength: 150,
						labelId: 'label_DSCPP___DSCPPSUBJECT_',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPPUBLISHE: new fieldControlClass.StringControl({
						modelField: 'ValPublisher',
						valueChangeEvent: 'fieldChange:dscpp.publisher',
						id: 'DSCPP___DSCPPPUBLISHE',
						name: 'PUBLISHE',
						size: 'xxlarge',
						label: computed(() => this.Resources.PUBLISHER59095),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDDESCRIPT',
						maxLength: 50,
						labelId: 'label_DSCPP___DSCPPPUBLISHE',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPCONTRIBU: new fieldControlClass.StringControl({
						modelField: 'ValContributor',
						valueChangeEvent: 'fieldChange:dscpp.contributor',
						id: 'DSCPP___DSCPPCONTRIBU',
						name: 'CONTRIBU',
						size: 'xxlarge',
						label: computed(() => this.Resources.CONTRIBUTOR40001),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDDESCRIPT',
						maxLength: 150,
						labelId: 'label_DSCPP___DSCPPCONTRIBU',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPTYPE____: new fieldControlClass.StringControl({
						modelField: 'ValType',
						valueChangeEvent: 'fieldChange:dscpp.type',
						id: 'DSCPP___DSCPPTYPE____',
						name: 'TYPE',
						size: 'block',
						label: computed(() => this.Resources.TYPE00312),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDDESCRIPT',
						maxLength: 150,
						labelId: 'label_DSCPP___DSCPPTYPE____',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPFORMAT__: new fieldControlClass.StringControl({
						modelField: 'ValFormat',
						valueChangeEvent: 'fieldChange:dscpp.format',
						id: 'DSCPP___DSCPPFORMAT__',
						name: 'FORMAT',
						size: 'xxlarge',
						label: computed(() => this.Resources.FORMAT00194),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDDESCRIPT',
						maxLength: 50,
						labelId: 'label_DSCPP___DSCPPFORMAT__',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPIDENTIFI: new fieldControlClass.StringControl({
						modelField: 'ValIdentifier',
						valueChangeEvent: 'fieldChange:dscpp.identifier',
						id: 'DSCPP___DSCPPIDENTIFI',
						name: 'IDENTIFI',
						size: 'xxlarge',
						label: computed(() => this.Resources.IDENTIFIER53792),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDDESCRIPT',
						isFormulaBlocked: true,
						maxLength: 150,
						labelId: 'label_DSCPP___DSCPPIDENTIFI',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPSOURCE__: new fieldControlClass.StringControl({
						modelField: 'ValSource',
						valueChangeEvent: 'fieldChange:dscpp.source',
						id: 'DSCPP___DSCPPSOURCE__',
						name: 'SOURCE',
						size: 'xxlarge',
						label: computed(() => this.Resources.SOURCE49690),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDDESCRIPT',
						maxLength: 150,
						labelId: 'label_DSCPP___DSCPPSOURCE__',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPRELATION: new fieldControlClass.StringControl({
						modelField: 'ValRelation',
						valueChangeEvent: 'fieldChange:dscpp.relation',
						id: 'DSCPP___DSCPPRELATION',
						name: 'RELATION',
						size: 'xxlarge',
						label: computed(() => this.Resources.RELATION40306),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDDESCRIPT',
						maxLength: 150,
						labelId: 'label_DSCPP___DSCPPRELATION',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPCOVERAGE: new fieldControlClass.StringControl({
						modelField: 'ValCoverage',
						valueChangeEvent: 'fieldChange:dscpp.coverage',
						id: 'DSCPP___DSCPPCOVERAGE',
						name: 'COVERAGE',
						size: 'xxlarge',
						label: computed(() => this.Resources.COVERAGE10098),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDDESCRIPT',
						maxLength: 150,
						labelId: 'label_DSCPP___DSCPPCOVERAGE',
						controlLimits: [
						],
					}, this),
					DSCPP___LANGULANGUISO: new fieldControlClass.LookupControl({
						modelField: 'TableLanguLanguiso',
						valueChangeEvent: 'fieldChange:langu.languageisocode',
						id: 'DSCPP___LANGULANGUISO',
						name: 'LANGUISO',
						size: 'small',
						label: computed(() => this.Resources.LANGUAGE_ISO_CODE59219),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDDESCRIPT',
						externalCallbacks: {
							getModelField: vm.getModelField,
							getModelFieldValue: vm.getModelFieldValue,
							setModelFieldValue: vm.setModelFieldValue
						},
						externalProperties: {
							modelKeys: computed(() => vm.modelKeys)
						},
						lookupKeyModelField: {
							name: 'ValCodlangu',
							dependencyEvent: 'fieldChange:dscpp.codlangu'
						},
						dependentFields: () => ({
							set 'langu.codlangu'(value) { vm.model.ValCodlangu.updateValue(value) },
							set 'langu.languageisocode'(value) { vm.model.TableLanguLanguiso.updateValue(value) },
						}),
						controlLimits: [
						],
					}, this),
					DSCPP___COUNTCOUNTRY_: new fieldControlClass.LookupControl({
						modelField: 'TableCountCountry',
						valueChangeEvent: 'fieldChange:count.country',
						id: 'DSCPP___COUNTCOUNTRY_',
						name: 'COUNTRY',
						size: 'xxlarge',
						label: computed(() => this.Resources.COUNTRY64133),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDDESCRIPT',
						externalCallbacks: {
							getModelField: vm.getModelField,
							getModelFieldValue: vm.getModelFieldValue,
							setModelFieldValue: vm.setModelFieldValue
						},
						externalProperties: {
							modelKeys: computed(() => vm.modelKeys)
						},
						lookupKeyModelField: {
							name: 'ValCodcount',
							dependencyEvent: 'fieldChange:dscpp.codcount'
						},
						dependentFields: () => ({
							set 'count.codcount'(value) { vm.model.ValCodcount.updateValue(value) },
							set 'count.country'(value) { vm.model.TableCountCountry.updateValue(value) },
						}),
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPRIGHTS__: new fieldControlClass.StringControl({
						modelField: 'ValRights',
						valueChangeEvent: 'fieldChange:dscpp.rights',
						id: 'DSCPP___DSCPPRIGHTS__',
						name: 'RIGHTS',
						size: 'xxlarge',
						label: computed(() => this.Resources.RIGHTS63991),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDDESCRIPT',
						isFormulaBlocked: true,
						maxLength: 150,
						labelId: 'label_DSCPP___DSCPPRIGHTS__',
						controlLimits: [
						],
					}, this),
					DSCPP___PSEUDZNIDENTI: new fieldControlClass.GroupControl({
						id: 'DSCPP___PSEUDZNIDENTI',
						name: 'ZNIDENTI',
						size: 'block',
						label: computed(() => this.Resources.IDENTIFICATION40793),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						isCollapsible: true,
						anchored: false,
						directChildren: ['DSCPP___DSCPPISSUENUM', 'DSCPP___DSCPPINWORK__', 'DSCPP___DSCPPEXTEPROD', 'DSCPP___DSCPPEXTECODE', 'DSCPP___DSCPPMODICODE', 'DSCPP___DSCPPSYSTEMDI', 'DSCPP___DMCDRCOMPLCOD', 'DSCPP___DSCPPLEARNCOD', 'DSCPP___DSCPPLEAREVCD', 'DSCPP___DSCPPTECHNAME', 'DSCPP___DSCPPINFONAME', 'DSCPP___DSCPPISSUETYP'],
						mustBeFilled: true,
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPISSUENUM: new fieldControlClass.StringControl({
						modelField: 'ValIssuenumber',
						valueChangeEvent: 'fieldChange:dscpp.issuenumber',
						id: 'DSCPP___DSCPPISSUENUM',
						name: 'ISSUENUM',
						size: 'medium',
						label: computed(() => this.Resources.ISSUE_NUMBER38761),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDZNIDENTI',
						maxLength: 3,
						labelId: 'label_DSCPP___DSCPPISSUENUM',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPINWORK__: new fieldControlClass.StringControl({
						modelField: 'ValInwork',
						valueChangeEvent: 'fieldChange:dscpp.inwork',
						id: 'DSCPP___DSCPPINWORK__',
						name: 'INWORK',
						size: 'mini',
						label: computed(() => this.Resources.IN_WORK08152),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDZNIDENTI',
						maxLength: 3,
						labelId: 'label_DSCPP___DSCPPINWORK__',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPEXTEPROD: new fieldControlClass.StringControl({
						modelField: 'ValExtensionproducer',
						valueChangeEvent: 'fieldChange:dscpp.extensionproducer',
						id: 'DSCPP___DSCPPEXTEPROD',
						name: 'EXTEPROD',
						size: 'xxlarge',
						label: computed(() => this.Resources.EXTENSION_PRODUCER50264),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDZNIDENTI',
						maxLength: 200,
						labelId: 'label_DSCPP___DSCPPEXTEPROD',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPEXTECODE: new fieldControlClass.StringControl({
						modelField: 'ValExtensioncode',
						valueChangeEvent: 'fieldChange:dscpp.extensioncode',
						id: 'DSCPP___DSCPPEXTECODE',
						name: 'EXTECODE',
						size: 'xxlarge',
						label: computed(() => this.Resources.EXTENSION_CODE33830),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDZNIDENTI',
						maxLength: 50,
						labelId: 'label_DSCPP___DSCPPEXTECODE',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPMODICODE: new fieldControlClass.StringControl({
						modelField: 'ValModelidentcode',
						valueChangeEvent: 'fieldChange:dscpp.modelidentcode',
						id: 'DSCPP___DSCPPMODICODE',
						name: 'MODICODE',
						size: 'medium',
						label: computed(() => this.Resources.MODEL_IDENTIFIER14950),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDZNIDENTI',
						maxLength: 16,
						labelId: 'label_DSCPP___DSCPPMODICODE',
						mustBeFilled: true,
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPSYSTEMDI: new fieldControlClass.StringControl({
						modelField: 'ValSystemdiffcode',
						valueChangeEvent: 'fieldChange:dscpp.systemdiffcode',
						id: 'DSCPP___DSCPPSYSTEMDI',
						name: 'SYSTEMDI',
						size: 'large',
						label: computed(() => this.Resources.SYSTEM_DIFFERENCE_CO01829),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDZNIDENTI',
						maxLength: 10,
						labelId: 'label_DSCPP___DSCPPSYSTEMDI',
						controlLimits: [
						],
					}, this),
					DSCPP___DMCDRCOMPLCOD: new fieldControlClass.LookupControl({
						modelField: 'TableDmcdrComplcod',
						valueChangeEvent: 'fieldChange:dmcdr.completecode',
						id: 'DSCPP___DMCDRCOMPLCOD',
						name: 'COMPLCOD',
						size: 'xxlarge',
						label: computed(() => this.Resources.CODE49225),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDZNIDENTI',
						externalCallbacks: {
							getModelField: vm.getModelField,
							getModelFieldValue: vm.getModelFieldValue,
							setModelFieldValue: vm.setModelFieldValue
						},
						externalProperties: {
							modelKeys: computed(() => vm.modelKeys)
						},
						lookupKeyModelField: {
							name: 'ValCoddmcod',
							dependencyEvent: 'fieldChange:dscpp.coddmcod'
						},
						dependentFields: () => ({
							set 'dmcdr.coddmcod'(value) { vm.model.ValCoddmcod.updateValue(value) },
							set 'dmcdr.completecode'(value) { vm.model.TableDmcdrComplcod.updateValue(value) },
						}),
						mustBeFilled: true,
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPLEARNCOD: new fieldControlClass.StringControl({
						modelField: 'ValLearncode',
						valueChangeEvent: 'fieldChange:dscpp.learncode',
						id: 'DSCPP___DSCPPLEARNCOD',
						name: 'LEARNCOD',
						size: 'xxlarge',
						label: computed(() => this.Resources.LEARN_CODE01421),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDZNIDENTI',
						maxLength: 50,
						labelId: 'label_DSCPP___DSCPPLEARNCOD',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPLEAREVCD: new fieldControlClass.StringControl({
						modelField: 'ValLearneventcode',
						valueChangeEvent: 'fieldChange:dscpp.learneventcode',
						id: 'DSCPP___DSCPPLEAREVCD',
						name: 'LEAREVCD',
						size: 'medium',
						label: computed(() => this.Resources.LEARN_EVENT_CODE58740),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDZNIDENTI',
						maxLength: 1,
						labelId: 'label_DSCPP___DSCPPLEAREVCD',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPTECHNAME: new fieldControlClass.StringControl({
						modelField: 'ValTechname',
						valueChangeEvent: 'fieldChange:dscpp.techname',
						id: 'DSCPP___DSCPPTECHNAME',
						name: 'TECHNAME',
						size: 'xxlarge',
						label: computed(() => this.Resources.TECHNICAL_NAME26958),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDZNIDENTI',
						maxLength: 150,
						labelId: 'label_DSCPP___DSCPPTECHNAME',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPINFONAME: new fieldControlClass.StringControl({
						modelField: 'ValInfoname',
						valueChangeEvent: 'fieldChange:dscpp.infoname',
						id: 'DSCPP___DSCPPINFONAME',
						name: 'INFONAME',
						size: 'xxlarge',
						label: computed(() => this.Resources.INFORMATION_NAME64067),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDZNIDENTI',
						maxLength: 150,
						labelId: 'label_DSCPP___DSCPPINFONAME',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPISSUETYP: new fieldControlClass.ArrayStringControl({
						modelField: 'ValIssuetype',
						valueChangeEvent: 'fieldChange:dscpp.issuetype',
						id: 'DSCPP___DSCPPISSUETYP',
						name: 'ISSUETYP',
						size: 'medium',
						label: computed(() => this.Resources.ISSUE_TYPE61054),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDZNIDENTI',
						maxLength: 2,
						labelId: 'label_DSCPP___DSCPPISSUETYP',
						arrayName: 'aIssueTp',
						helpShortItem: '',
						helpDetailedItem: '',
						controlLimits: [
						],
					}, this),
					DSCPP___PSEUDZNDOCUM_: new fieldControlClass.GroupControl({
						id: 'DSCPP___PSEUDZNDOCUM_',
						name: 'ZNDOCUM',
						size: 'block',
						label: computed(() => this.Resources.DOCUMENTS28729),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						isCollapsible: true,
						anchored: false,
						directChildren: ['DSCPP___PSEUDFICWEB1_'],
						controlLimits: [
						],
					}, this),
					DSCPP___PSEUDFICWEB1_: new fieldControlClass.TableListControl({
						id: 'DSCPP___PSEUDFICWEB1_',
						name: 'FICWEB1',
						size: 'block',
						label: '',
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDZNDOCUM_',
						controller: 'DSCPP',
						action: 'Dscpp_ValFicweb1',
						hasDependencies: false,
						isInCollapsible: true,
						columnsOriginal: [
							new listColumnTypes.DocumentColumn({
								order: 1,
								name: 'ValDocum',
								area: 'DOCUM',
								field: 'DOCUM',
								label: computed(() => this.Resources.FILE07547),
								dataLength: 255,
								scrollData: 30,
								sortable: false,
								viewType: qEnums.documentViewTypeMode.preview,
							}, computed(() => vm.model), computed(() => vm.internalEvents)),
							new listColumnTypes.TextColumn({
								order: 2,
								name: 'ValDocname',
								area: 'DOCUM',
								field: 'DOCNAME',
								label: computed(() => this.Resources.FILE_NAME37493),
								dataLength: 255,
								scrollData: 30,
							}, computed(() => vm.model), computed(() => vm.internalEvents)),
							new listColumnTypes.TextColumn({
								order: 3,
								name: 'ValLinkfile',
								area: 'DOCUM',
								field: 'LINKFILE',
								label: computed(() => this.Resources.LINK_TO_FILE35721),
								dataLength: 255,
								scrollData: 30,
							}, computed(() => vm.model), computed(() => vm.internalEvents)),
						],
						config: {
							name: 'ValFicweb1',
							serverMode: true,
							pkColumn: 'ValCoddocum',
							tableAlias: 'DOCUM',
							tableNamePlural: computed(() => this.Resources.DOCUMENTS14470),
							viewManagement: '',
							showLimitsInfo: true,
							showAlternatePagination: true,
							permissions: {
							},
							searchBarConfig: {
								visibility: false,
								searchOnPressEnter: true
							},
							filtersVisible: false,
							allowColumnFilters: false,
							allowColumnSort: true,
							crudActions: [
								{
									id: 'show',
									name: 'show',
									title: computed(() => this.Resources.CONSULTAR57388),
									icon: {
										icon: 'view'
									},
									isInReadOnly: true,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'DOCUM',
										mode: 'SHOW',
										isControlled: true
									}
								},
								{
									id: 'edit',
									name: 'edit',
									title: computed(() => this.Resources.EDITAR11616),
									icon: {
										icon: 'pencil'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'DOCUM',
										mode: 'EDIT',
										isControlled: true
									}
								},
								{
									id: 'duplicate',
									name: 'duplicate',
									title: computed(() => this.Resources.DUPLICAR09748),
									icon: {
										icon: 'duplicate'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'DOCUM',
										mode: 'DUPLICATE',
										isControlled: true
									}
								},
								{
									id: 'delete',
									name: 'delete',
									title: computed(() => this.Resources.ELIMINAR21155),
									icon: {
										icon: 'delete'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'DOCUM',
										mode: 'DELETE',
										isControlled: true
									}
								}
							],
							generalActions: [
								{
									id: 'insert',
									name: 'insert',
									title: 'Upload',
									icon: {
                                        icon: 'add'
									},
									isInReadOnly: false,
									click: function() { 
										// Emite um evento personalizado que será capturado pelo componente pai
										return { action: 'open-multi-upload' };
									}
								},
							],
							generalCustomActions: [
							],
							groupActions: [
							],
							customActions: [
							],
							MCActions: [
							],
							rowClickAction: {
								id: 'RCA__DOCUM',
								name: '_DOCUM',
								title: '',
								isInReadOnly: true,
								params: {
									isRoute: true,
									action: vm.openFormAction,
									type: 'form',
									formName: 'DOCUM',
									mode: 'SHOW',
									isControlled: true
								}
							},
							formsDefinition: {
								'DOCUM': {
									fnKeySelector: (row) => row.Fields.ValCoddocum,
									isPopup: true
								},
							},
							defaultSearchColumnName: '',
							defaultSearchColumnNameOriginal: '',
							defaultColumnSorting: {
								columnName: '',
								sortOrder: 'asc'
							}
						},
						globalEvents: ['changed-DOCUM', 'changed-DSCPP'],
						uuid: 'Dscpp_ValFicweb1',
						allSelectedRows: 'false',
						controlLimits: [
							{
								identifier: ['id', 'dscpp'],
								dependencyEvents: ['fieldChange:dscpp.coddscrp'],
								dependencyField: 'DSCPP.CODDSCRP',
								fnValueSelector: (model) => model.ValCoddscrp.value
							},
						],
					}, this),
					DSCPP___PSEUDNOVOGR01: new fieldControlClass.GroupControl({
						id: 'DSCPP___PSEUDNOVOGR01',
						name: 'NOVOGR01',
						size: 'block',
						label: computed(() => this.Resources.SECURITY63893),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						isCollapsible: true,
						anchored: false,
						directChildren: ['DSCPP___DSCPPSECURCLA', 'DSCPP___DSCPPSECCOCLA', 'DSCPP___DSCPPSECCAVAT'],
						mustBeFilled: true,
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPSECURCLA: new fieldControlClass.ArrayStringControl({
						modelField: 'ValSecurcla',
						valueChangeEvent: 'fieldChange:dscpp.securcla',
						id: 'DSCPP___DSCPPSECURCLA',
						name: 'SECURCLA',
						size: 'mini',
						label: computed(() => this.Resources.SECURITY_CLASSIFICAT49492),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDNOVOGR01',
						maxLength: 2,
						labelId: 'label_DSCPP___DSCPPSECURCLA',
						mustBeFilled: true,
						arrayName: 'aSecClas',
						helpShortItem: '',
						helpDetailedItem: '',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPSECCOCLA: new fieldControlClass.ArrayStringControl({
						modelField: 'ValSeccocla',
						valueChangeEvent: 'fieldChange:dscpp.seccocla',
						id: 'DSCPP___DSCPPSECCOCLA',
						name: 'SECCOCLA',
						size: 'small',
						label: computed(() => this.Resources.COMERCIAL_CLASSIFICA26746),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDNOVOGR01',
						maxLength: 4,
						labelId: 'label_DSCPP___DSCPPSECCOCLA',
						arrayName: 'aSCoClas',
						helpShortItem: '',
						helpDetailedItem: '',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPSECCAVAT: new fieldControlClass.ArrayStringControl({
						modelField: 'ValSeccavat',
						valueChangeEvent: 'fieldChange:dscpp.seccavat',
						id: 'DSCPP___DSCPPSECCAVAT',
						name: 'SECCAVAT',
						size: 'small',
						label: computed(() => this.Resources.CAVEAT43987),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDNOVOGR01',
						maxLength: 4,
						labelId: 'label_DSCPP___DSCPPSECCAVAT',
						arrayName: 'aCaveat',
						helpShortItem: '',
						helpDetailedItem: '',
						controlLimits: [
						],
					}, this),
					DSCPP___PSEUDSTATUS__: new fieldControlClass.GroupControl({
						id: 'DSCPP___PSEUDSTATUS__',
						name: 'STATUS',
						size: 'block',
						label: computed(() => this.Resources.STATUS08858),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						isCollapsible: true,
						anchored: false,
						directChildren: ['DSCPP___DSCPPDATADIST', 'DSCPP___DSCPPEXPREGST', 'DSCPP___DSCPPDATAHAND', 'DSCPP___DSCPPDATADEST', 'DSCPP___DSCPPDATADISC', 'DSCPP___DSCPPCOPYRIGH', 'DSCPP___DSCPPPOLICYST', 'DSCPP___DSCPPDATACOND', 'DSCPP___COMPRNAME____', 'DSCPP___COMPONAME____', 'DSCPP___PSEUDCOMPONEN'],
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPDATADIST: new fieldControlClass.StringControl({
						modelField: 'ValDatadistribution',
						valueChangeEvent: 'fieldChange:dscpp.datadistribution',
						id: 'DSCPP___DSCPPDATADIST',
						name: 'DATADIST',
						size: 'xxlarge',
						label: computed(() => this.Resources.DATA_DISTRIBUTION64392),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDSTATUS__',
						maxLength: 200,
						labelId: 'label_DSCPP___DSCPPDATADIST',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPEXPREGST: new fieldControlClass.MultilineStringControl({
						modelField: 'ValExportregistrationstmt',
						valueChangeEvent: 'fieldChange:dscpp.exportregistrationstmt',
						id: 'DSCPP___DSCPPEXPREGST',
						name: 'EXPREGST',
						size: 'xxlarge',
						label: computed(() => this.Resources.EXPORT_REGISTRATION50113),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDSTATUS__',
						rows: 1,
						cols: 500,
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPDATAHAND: new fieldControlClass.StringControl({
						modelField: 'ValDatahandling',
						valueChangeEvent: 'fieldChange:dscpp.datahandling',
						id: 'DSCPP___DSCPPDATAHAND',
						name: 'DATAHAND',
						size: 'xxlarge',
						label: computed(() => this.Resources.DATA_HANDLING32139),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDSTATUS__',
						maxLength: 500,
						labelId: 'label_DSCPP___DSCPPDATAHAND',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPDATADEST: new fieldControlClass.StringControl({
						modelField: 'ValDatadestruction',
						valueChangeEvent: 'fieldChange:dscpp.datadestruction',
						id: 'DSCPP___DSCPPDATADEST',
						name: 'DATADEST',
						size: 'xxlarge',
						label: computed(() => this.Resources.DATA_DESTRUCTION60928),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDSTATUS__',
						maxLength: 500,
						labelId: 'label_DSCPP___DSCPPDATADEST',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPDATADISC: new fieldControlClass.StringControl({
						modelField: 'ValDatadisclosure',
						valueChangeEvent: 'fieldChange:dscpp.datadisclosure',
						id: 'DSCPP___DSCPPDATADISC',
						name: 'DATADISC',
						size: 'xxlarge',
						label: computed(() => this.Resources.DATA_DISCLOSURE50470),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDSTATUS__',
						maxLength: 500,
						labelId: 'label_DSCPP___DSCPPDATADISC',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPCOPYRIGH: new fieldControlClass.TextEditorControl({
						modelField: 'ValCopyrigh',
						valueChangeEvent: 'fieldChange:dscpp.copyrigh',
						id: 'DSCPP___DSCPPCOPYRIGH',
						name: 'COPYRIGH',
						size: 'block',
						label: computed(() => this.Resources.COPYRIGHT58510),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDSTATUS__',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPPOLICYST: new fieldControlClass.StringControl({
						modelField: 'ValPolicystatement',
						valueChangeEvent: 'fieldChange:dscpp.policystatement',
						id: 'DSCPP___DSCPPPOLICYST',
						name: 'POLICYST',
						size: 'xxlarge',
						label: computed(() => this.Resources.POLICY_STATEMENT43958),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDSTATUS__',
						maxLength: 500,
						labelId: 'label_DSCPP___DSCPPPOLICYST',
						controlLimits: [
						],
					}, this),
					DSCPP___DSCPPDATACOND: new fieldControlClass.StringControl({
						modelField: 'ValDataconds',
						valueChangeEvent: 'fieldChange:dscpp.dataconds',
						id: 'DSCPP___DSCPPDATACOND',
						name: 'DATACOND',
						size: 'xxlarge',
						label: computed(() => this.Resources.DATA_CONDITIONS08191),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDSTATUS__',
						maxLength: 500,
						labelId: 'label_DSCPP___DSCPPDATACOND',
						controlLimits: [
						],
					}, this),
					DSCPP___COMPRNAME____: new fieldControlClass.LookupControl({
						modelField: 'TableComprName',
						valueChangeEvent: 'fieldChange:compr.name',
						id: 'DSCPP___COMPRNAME____',
						name: 'NAME',
						size: 'xxlarge',
						label: computed(() => this.Resources.RESPONSIBLE_PARTNER_49442),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDSTATUS__',
						externalCallbacks: {
							getModelField: vm.getModelField,
							getModelFieldValue: vm.getModelFieldValue,
							setModelFieldValue: vm.setModelFieldValue
						},
						externalProperties: {
							modelKeys: computed(() => vm.modelKeys)
						},
						lookupKeyModelField: {
							name: 'ValCodcompr',
							dependencyEvent: 'fieldChange:dscpp.codcompr'
						},
						dependentFields: () => ({
							set 'compr.codcompy'(value) { vm.model.ValCodcompr.updateValue(value) },
							set 'compr.name'(value) { vm.model.TableComprName.updateValue(value) },
						}),
						controlLimits: [
						],
					}, this),
					DSCPP___COMPONAME____: new fieldControlClass.LookupControl({
						modelField: 'TableCompoName',
						valueChangeEvent: 'fieldChange:compo.name',
						id: 'DSCPP___COMPONAME____',
						name: 'NAME',
						size: 'xxlarge',
						label: computed(() => this.Resources.ENTERPRISE_NAME50437),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDSTATUS__',
						externalCallbacks: {
							getModelField: vm.getModelField,
							getModelFieldValue: vm.getModelFieldValue,
							setModelFieldValue: vm.setModelFieldValue
						},
						externalProperties: {
							modelKeys: computed(() => vm.modelKeys)
						},
						lookupKeyModelField: {
							name: 'ValCodcompo',
							dependencyEvent: 'fieldChange:dscpp.codcompo'
						},
						dependentFields: () => ({
							set 'compo.codcompy'(value) { vm.model.ValCodcompo.updateValue(value) },
							set 'compo.name'(value) { vm.model.TableCompoName.updateValue(value) },
						}),
						controlLimits: [
						],
					}, this),
					DSCPP___PSEUDCOMPONEN: new fieldControlClass.TableListControl({
						id: 'DSCPP___PSEUDCOMPONEN',
						name: 'COMPONEN',
						size: 'block',
						label: computed(() => this.Resources.APPLICABILITY_CROSS_55316),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDSTATUS__',
						controller: 'DSCPP',
						action: 'Dscpp_ValComponen',
						hasDependencies: false,
						isInCollapsible: true,
						columnsOriginal: [
							new listColumnTypes.TextColumn({
								order: 1,
								name: 'Dscrc.ValIdentifier',
								area: 'DSCRC',
								field: 'IDENTIFIER',
								label: computed(() => this.Resources.COMPONENT44370),
								dataLength: 150,
								scrollData: 30,
								pkColumn: 'ValCoddscrp',
							}, computed(() => vm.model), computed(() => vm.internalEvents)),
						],
						config: {
							name: 'ValComponen',
							serverMode: true,
							pkColumn: 'ValCodrldsc',
							tableAlias: 'RLDSC',
							tableNamePlural: computed(() => this.Resources.REFERENCES_DESCRIPTS60534),
							viewManagement: '',
							showLimitsInfo: true,
							tableTitle: computed(() => this.Resources.APPLICABILITY_CROSS_55316),
							showAlternatePagination: true,
							permissions: {
							},
							searchBarConfig: {
								visibility: false,
								searchOnPressEnter: true
							},
							filtersVisible: false,
							allowColumnFilters: false,
							allowColumnSort: true,
							crudActions: [
								{
									id: 'show',
									name: 'show',
									title: computed(() => this.Resources.CONSULTAR57388),
									icon: {
										icon: 'view'
									},
									isInReadOnly: true,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'RLDSC',
										mode: 'SHOW',
										isControlled: true
									}
								},
								{
									id: 'edit',
									name: 'edit',
									title: computed(() => this.Resources.EDITAR11616),
									icon: {
										icon: 'pencil'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'RLDSC',
										mode: 'EDIT',
										isControlled: true
									}
								},
								{
									id: 'duplicate',
									name: 'duplicate',
									title: computed(() => this.Resources.DUPLICAR09748),
									icon: {
										icon: 'duplicate'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'RLDSC',
										mode: 'DUPLICATE',
										isControlled: true
									}
								},
								{
									id: 'delete',
									name: 'delete',
									title: computed(() => this.Resources.ELIMINAR21155),
									icon: {
										icon: 'delete'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'RLDSC',
										mode: 'DELETE',
										isControlled: true
									}
								}
							],
							generalActions: [
								{
									id: 'insert',
									name: 'insert',
									title: computed(() => this.Resources.INSERIR43365),
									icon: {
										icon: 'add'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'RLDSC',
										mode: 'NEW',
										repeatInsertion: false,
										isControlled: true
									}
								},
							],
							generalCustomActions: [
							],
							groupActions: [
							],
							customActions: [
							],
							MCActions: [
							],
							rowClickAction: {
								id: 'RCA__RLDSC',
								name: '_RLDSC',
								title: '',
								isInReadOnly: true,
								params: {
									isRoute: true,
									action: vm.openFormAction,
									type: 'form',
									formName: 'RLDSC',
									mode: 'SHOW',
									isControlled: true
								}
							},
							formsDefinition: {
								'RLDSC': {
									fnKeySelector: (row) => row.Fields.ValCodrldsc,
									isPopup: false
								},
							},
							defaultSearchColumnName: '',
							defaultSearchColumnNameOriginal: '',
							defaultColumnSorting: {
								columnName: '',
								sortOrder: 'asc'
							}
						},
						globalEvents: ['changed-RLDSC', 'changed-DSCRC', 'changed-DSCPP'],
						uuid: 'Dscpp_ValComponen',
						allSelectedRows: 'false',
						controlLimits: [
							{
								identifier: ['id', 'dscpp'],
								dependencyEvents: ['fieldChange:dscpp.coddscrp'],
								dependencyField: 'DSCPP.CODDSCRP',
								fnValueSelector: (model) => model.ValCoddscrp.value
							},
						],
					}, this),
					DSCPP___PSEUDAPPLICAB: new fieldControlClass.GroupControl({
						id: 'DSCPP___PSEUDAPPLICAB',
						name: 'APPLICAB',
						size: 'block',
						label: computed(() => this.Resources.APPLICABILITY25450),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						isCollapsible: true,
						anchored: false,
						directChildren: ['DSCPP___PSEUDAPPS____'],
						controlLimits: [
						],
					}, this),
					DSCPP___PSEUDAPPS____: new fieldControlClass.TableListControl({
						id: 'DSCPP___PSEUDAPPS____',
						name: 'APPS',
						size: 'block',
						label: computed(() => this.Resources.APPLIC_OF_THE_DESCRI27127),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDAPPLICAB',
						controller: 'DSCPP',
						action: 'Dscpp_ValApps',
						hasDependencies: false,
						isInCollapsible: true,
						columnsOriginal: [
							new listColumnTypes.TextColumn({
								order: 1,
								name: 'ValDisplaytext',
								area: 'APPLP',
								field: 'DISPLAYTEXT',
								label: computed(() => this.Resources.DISPLAY_TEXT26895),
								dataLength: 150,
								scrollData: 30,
							}, computed(() => vm.model), computed(() => vm.internalEvents)),
						],
						config: {
							name: 'ValApps',
							serverMode: true,
							pkColumn: 'ValCodappli',
							tableAlias: 'APPLP',
							tableNamePlural: computed(() => this.Resources.APPLIC_OF_THE_DESCRI27127),
							viewManagement: '',
							showLimitsInfo: true,
							tableTitle: computed(() => this.Resources.APPLIC_OF_THE_DESCRI27127),
							showAlternatePagination: true,
							permissions: {
							},
							searchBarConfig: {
								visibility: false,
								searchOnPressEnter: true
							},
							filtersVisible: false,
							allowColumnFilters: false,
							allowColumnSort: true,
							crudActions: [
								{
									id: 'show',
									name: 'show',
									title: computed(() => this.Resources.CONSULTAR57388),
									icon: {
										icon: 'view'
									},
									isInReadOnly: true,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'APPLP',
										mode: 'SHOW',
										isControlled: true
									}
								},
								{
									id: 'edit',
									name: 'edit',
									title: computed(() => this.Resources.EDITAR11616),
									icon: {
										icon: 'pencil'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'APPLP',
										mode: 'EDIT',
										isControlled: true
									}
								},
								{
									id: 'duplicate',
									name: 'duplicate',
									title: computed(() => this.Resources.DUPLICAR09748),
									icon: {
										icon: 'duplicate'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'APPLP',
										mode: 'DUPLICATE',
										isControlled: true
									}
								},
								{
									id: 'delete',
									name: 'delete',
									title: computed(() => this.Resources.ELIMINAR21155),
									icon: {
										icon: 'delete'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'APPLP',
										mode: 'DELETE',
										isControlled: true
									}
								}
							],
							generalActions: [
								{
									id: 'insert',
									name: 'insert',
									title: computed(() => this.Resources.INSERIR43365),
									icon: {
										icon: 'add'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'APPLP',
										mode: 'NEW',
										repeatInsertion: false,
										isControlled: true
									}
								},
							],
							generalCustomActions: [
							],
							groupActions: [
							],
							customActions: [
							],
							MCActions: [
							],
							rowClickAction: {
								id: 'RCA__APPLP',
								name: '_APPLP',
								title: '',
								isInReadOnly: true,
								params: {
									isRoute: true,
									action: vm.openFormAction,
									type: 'form',
									formName: 'APPLP',
									mode: 'SHOW',
									isControlled: true
								}
							},
							formsDefinition: {
								'APPLP': {
									fnKeySelector: (row) => row.Fields.ValCodappli,
									isPopup: false
								},
							},
							defaultSearchColumnName: '',
							defaultSearchColumnNameOriginal: '',
							defaultColumnSorting: {
								columnName: '',
								sortOrder: 'asc'
							}
						},
						globalEvents: ['changed-DSCPP', 'changed-APPLP'],
						uuid: 'Dscpp_ValApps',
						allSelectedRows: 'false',
						controlLimits: [
							{
								identifier: ['id', 'dscpp'],
								dependencyEvents: ['fieldChange:dscpp.coddscrp'],
								dependencyField: 'DSCPP.CODDSCRP',
								fnValueSelector: (model) => model.ValCoddscrp.value
							},
						],
					}, this),
					DSCPP___PSEUDVERSIONS: new fieldControlClass.GroupControl({
						id: 'DSCPP___PSEUDVERSIONS',
						name: 'VERSIONS',
						size: 'block',
						label: computed(() => this.Resources.VERSIONS21770),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						isCollapsible: true,
						anchored: false,
						directChildren: ['DSCPP___PSEUDEXPVRSN_'],
						controlLimits: [
						],
					}, this),
					DSCPP___PSEUDEXPVRSN_: new fieldControlClass.TableListControl({
						id: 'DSCPP___PSEUDEXPVRSN_',
						name: 'EXPVRSN',
						size: 'block',
						label: '',
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDVERSIONS',
						controller: 'DSCPP',
						action: 'Dscpp_ValExpvrsn',
						hasDependencies: false,
						isInCollapsible: true,
						columnsOriginal: [
							new listColumnTypes.TextColumn({
								order: 1,
								name: 'ValIdentifier',
								area: 'DSCRC',
								field: 'IDENTIFIER',
								label: computed(() => this.Resources.IDENTIFIER53792),
								dataLength: 150,
								scrollData: 100,
							}, computed(() => vm.model), computed(() => vm.internalEvents)),
							new listColumnTypes.NumericColumn({
								order: 2,
								name: 'ValNumversi',
								area: 'DSCRC',
								field: 'NUMVERSI',
								label: computed(() => this.Resources.VERSION35066),
								scrollData: 15,
								maxDigits: 15,
								decimalPlaces: 0,
							}, computed(() => vm.model), computed(() => vm.internalEvents)),
						],
						config: {
							name: 'ValExpvrsn',
							serverMode: true,
							pkColumn: 'ValCoddscrp',
							tableAlias: 'DSCRC',
							tableNamePlural: computed(() => this.Resources.DESCRIPTIVES_CHILDRE19328),
							viewManagement: '',
							showLimitsInfo: true,
							showAlternatePagination: true,
							permissions: {
							},
							searchBarConfig: {
								visibility: false,
								searchOnPressEnter: true
							},
							filtersVisible: false,
							allowColumnFilters: false,
							allowColumnSort: true,
							generalCustomActions: [
							],
							groupActions: [
							],
							customActions: [
							],
							MCActions: [
							],
							rowClickAction: {
							},
							formsDefinition: {
							},
							defaultSearchColumnName: '',
							defaultSearchColumnNameOriginal: '',
							defaultColumnSorting: {
								columnName: 'ValNumversi',
								sortOrder: 'desc'
							}
						},
						globalEvents: ['changed-COMPR', 'changed-COMPO', 'changed-DSCRC', 'changed-LANGU', 'changed-COUNT', 'changed-DMCDR'],
						uuid: 'Dscpp_ValExpvrsn',
						allSelectedRows: 'false',
						controlLimits: [
							{
								identifier: ['id', 'dscpp'],
								dependencyEvents: ['fieldChange:dscpp.coddscrp'],
								dependencyField: 'DSCPP.CODDSCRP',
								fnValueSelector: (model) => model.ValCoddscrp.value
							},
						],
					}, this),
					DSCPP___PSEUDZNIETDD_: new fieldControlClass.GroupControl({
						id: 'DSCPP___PSEUDZNIETDD_',
						name: 'ZNIETDD',
						size: 'block',
						label: computed(() => this.Resources.IETD_INFORMATION_REC48818),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						isCollapsible: true,
						anchored: false,
						directChildren: ['DSCPP___PSEUDLSTIETDD'],
						controlLimits: [
						],
					}, this),
					DSCPP___PSEUDLSTIETDD: new fieldControlClass.TableListControl({
						id: 'DSCPP___PSEUDLSTIETDD',
						name: 'LSTIETDD',
						size: 'block',
						label: computed(() => this.Resources.IETD_INFORMATION_REC45178),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'DSCPP___PSEUDZNIETDD_',
						controller: 'DSCPP',
						action: 'Dscpp_ValLstietdd',
						hasDependencies: false,
						isInCollapsible: true,
						columnsOriginal: [
							new listColumnTypes.TextColumn({
								order: 1,
								name: 'ValDrawingnumber',
								area: 'IETDD',
								field: 'DRAWINGNUMBER',
								label: computed(() => this.Resources.DRAWING_NUMBER36952),
								dataLength: 100,
								scrollData: 30,
							}, computed(() => vm.model), computed(() => vm.internalEvents)),
							new listColumnTypes.TextColumn({
								order: 2,
								name: 'ValDrawingrevision',
								area: 'IETDD',
								field: 'DRAWINGREVISION',
								label: computed(() => this.Resources.DRAWING_REVISION37446),
								dataLength: 50,
								scrollData: 30,
							}, computed(() => vm.model), computed(() => vm.internalEvents)),
							new listColumnTypes.TextColumn({
								order: 3,
								name: 'ValNsnident',
								area: 'IETDD',
								field: 'NSNIDENT',
								label: computed(() => this.Resources.NATO_STOCK_NUMBER__N19436),
								dataLength: 100,
								scrollData: 30,
							}, computed(() => vm.model), computed(() => vm.internalEvents)),
							new listColumnTypes.BooleanColumn({
								order: 4,
								name: 'ValApproved',
								area: 'IETDD',
								field: 'APPROVED',
								label: computed(() => this.Resources.APPROVED54787),
								scrollData: 1,
							}, computed(() => vm.model), computed(() => vm.internalEvents)),
							new listColumnTypes.BooleanColumn({
								order: 5,
								name: 'ValUrgenttreatmentrequired',
								area: 'IETDD',
								field: 'URGENTTREATMENTREQUIRED',
								label: computed(() => this.Resources.URGENT_TREATMENT_REQ03443),
								scrollData: 1,
							}, computed(() => vm.model), computed(() => vm.internalEvents)),
							new listColumnTypes.TextColumn({
								order: 6,
								name: 'ValIetdrevisionnumber',
								area: 'IETDD',
								field: 'IETDREVISIONNUMBER',
								label: computed(() => this.Resources.IETD_REVISION_NUMBER35938),
								dataLength: 50,
								scrollData: 30,
							}, computed(() => vm.model), computed(() => vm.internalEvents)),
						],
						config: {
							name: 'ValLstietdd',
							serverMode: true,
							pkColumn: 'ValCodietdd',
							tableAlias: 'IETDD',
							tableNamePlural: computed(() => this.Resources.IETD_APPENDIX_D26110),
							viewManagement: '',
							showLimitsInfo: true,
							tableTitle: computed(() => this.Resources.IETD_INFORMATION_REC45178),
							showAlternatePagination: true,
							permissions: {
							},
							searchBarConfig: {
								visibility: false,
								searchOnPressEnter: true
							},
							filtersVisible: false,
							allowColumnFilters: false,
							allowColumnSort: true,
							crudActions: [
								{
									id: 'show',
									name: 'show',
									title: computed(() => this.Resources.CONSULTAR57388),
									icon: {
										icon: 'view'
									},
									isInReadOnly: true,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'IETDD',
										mode: 'SHOW',
										isControlled: true
									}
								},
								{
									id: 'edit',
									name: 'edit',
									title: computed(() => this.Resources.EDITAR11616),
									icon: {
										icon: 'pencil'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'IETDD',
										mode: 'EDIT',
										isControlled: true
									}
								},
								{
									id: 'duplicate',
									name: 'duplicate',
									title: computed(() => this.Resources.DUPLICAR09748),
									icon: {
										icon: 'duplicate'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'IETDD',
										mode: 'DUPLICATE',
										isControlled: true
									}
								},
								{
									id: 'delete',
									name: 'delete',
									title: computed(() => this.Resources.ELIMINAR21155),
									icon: {
										icon: 'delete'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'IETDD',
										mode: 'DELETE',
										isControlled: true
									}
								}
							],
							generalActions: [
								{
									id: 'insert',
									name: 'insert',
									title: computed(() => this.Resources.INSERIR43365),
									icon: {
										icon: 'add'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'IETDD',
										mode: 'NEW',
										repeatInsertion: false,
										isControlled: true
									}
								},
							],
							generalCustomActions: [
							],
							groupActions: [
							],
							customActions: [
							],
							MCActions: [
							],
							rowClickAction: {
								id: 'RCA__IETDD',
								name: '_IETDD',
								title: '',
								isInReadOnly: true,
								params: {
									isRoute: true,
									action: vm.openFormAction,
									type: 'form',
									formName: 'IETDD',
									mode: 'SHOW',
									isControlled: true
								}
							},
							formsDefinition: {
								'IETDD': {
									fnKeySelector: (row) => row.Fields.ValCodietdd,
									isPopup: false
								},
							},
							defaultSearchColumnName: 'ValDrawingnumber',
							defaultSearchColumnNameOriginal: 'ValDrawingnumber',
							defaultColumnSorting: {
								columnName: 'ValDrawingnumber',
								sortOrder: 'asc'
							}
						},
						globalEvents: ['changed-IETDD', 'changed-DSCPP'],
						uuid: 'Dscpp_ValLstietdd',
						allSelectedRows: 'false',
						controlLimits: [
							{
								identifier: ['id', 'dscpp'],
								dependencyEvents: ['fieldChange:dscpp.coddscrp'],
								dependencyField: 'DSCPP.CODDSCRP',
								fnValueSelector: (model) => model.ValCoddscrp.value
							},
						],
					}, this),
				},

				model: new FormViewModel(this, {
					callbacks: {
						onUpdate: this.onUpdate,
						setFormKey: this.setFormKey
					}
				}),

				groupFields: readonly([
					'DSCPP___PSEUDDESCRIPT',
					'DSCPP___PSEUDZNIDENTI',
					'DSCPP___PSEUDZNDOCUM_',
					'DSCPP___PSEUDNOVOGR01',
					'DSCPP___PSEUDSTATUS__',
					'DSCPP___PSEUDAPPLICAB',
					'DSCPP___PSEUDVERSIONS',
					'DSCPP___PSEUDZNIETDD_',
				]),

				tableFields: readonly([
					'DSCPP___PSEUDFICWEB1_',
					'DSCPP___PSEUDCOMPONEN',
					'DSCPP___PSEUDAPPS____',
					'DSCPP___PSEUDEXPVRSN_',
					'DSCPP___PSEUDLSTIETDD',
				]),

				timelineFields: readonly([
				]),

				/**
				 * The Data API for easy access to model variables.
				 */
				dataApi: {
					Compo: {
						get ValName() { return vm.model.TableCompoName.value },
						set ValName(value) { vm.model.TableCompoName.updateValue(value) },
					},
					Compr: {
						get ValName() { return vm.model.TableComprName.value },
						set ValName(value) { vm.model.TableComprName.updateValue(value) },
					},
					Count: {
						get ValCountry() { return vm.model.TableCountCountry.value },
						set ValCountry(value) { vm.model.TableCountCountry.updateValue(value) },
					},
					Dmcdr: {
						get ValCompletecode() { return vm.model.TableDmcdrComplcod.value },
						set ValCompletecode(value) { vm.model.TableDmcdrComplcod.updateValue(value) },
					},
					Dscpp: {
						get ValCodcompo() { return vm.model.ValCodcompo.value },
						set ValCodcompo(value) { vm.model.ValCodcompo.updateValue(value) },
						get ValCodcompr() { return vm.model.ValCodcompr.value },
						set ValCodcompr(value) { vm.model.ValCodcompr.updateValue(value) },
						get ValCodcount() { return vm.model.ValCodcount.value },
						set ValCodcount(value) { vm.model.ValCodcount.updateValue(value) },
						get ValCoddmcod() { return vm.model.ValCoddmcod.value },
						set ValCoddmcod(value) { vm.model.ValCoddmcod.updateValue(value) },
						get ValCodlangu() { return vm.model.ValCodlangu.value },
						set ValCodlangu(value) { vm.model.ValCodlangu.updateValue(value) },
						get ValCodversi() { return vm.model.ValCodversi.value },
						set ValCodversi(value) { vm.model.ValCodversi.updateValue(value) },
						get ValComplies() { return vm.model.ValComplies.value },
						set ValComplies(value) { vm.model.ValComplies.updateValue(value) },
						get ValContributor() { return vm.model.ValContributor.value },
						set ValContributor(value) { vm.model.ValContributor.updateValue(value) },
						get ValCopyrigh() { return vm.model.ValCopyrigh.value },
						set ValCopyrigh(value) { vm.model.ValCopyrigh.updateValue(value) },
						get ValCoverage() { return vm.model.ValCoverage.value },
						set ValCoverage(value) { vm.model.ValCoverage.updateValue(value) },
						get ValCreator() { return vm.model.ValCreator.value },
						set ValCreator(value) { vm.model.ValCreator.updateValue(value) },
						get ValDataconds() { return vm.model.ValDataconds.value },
						set ValDataconds(value) { vm.model.ValDataconds.updateValue(value) },
						get ValDatadestruction() { return vm.model.ValDatadestruction.value },
						set ValDatadestruction(value) { vm.model.ValDatadestruction.updateValue(value) },
						get ValDatadisclosure() { return vm.model.ValDatadisclosure.value },
						set ValDatadisclosure(value) { vm.model.ValDatadisclosure.updateValue(value) },
						get ValDatadistribution() { return vm.model.ValDatadistribution.value },
						set ValDatadistribution(value) { vm.model.ValDatadistribution.updateValue(value) },
						get ValDatahandling() { return vm.model.ValDatahandling.value },
						set ValDatahandling(value) { vm.model.ValDatahandling.updateValue(value) },
						get ValDate() { return vm.model.ValDate.value },
						set ValDate(value) { vm.model.ValDate.updateValue(value) },
						get ValDay() { return vm.model.ValDay.value },
						set ValDay(value) { vm.model.ValDay.updateValue(value) },
						get ValExportregistrationstmt() { return vm.model.ValExportregistrationstmt.value },
						set ValExportregistrationstmt(value) { vm.model.ValExportregistrationstmt.updateValue(value) },
						get ValExtensioncode() { return vm.model.ValExtensioncode.value },
						set ValExtensioncode(value) { vm.model.ValExtensioncode.updateValue(value) },
						get ValExtensionproducer() { return vm.model.ValExtensionproducer.value },
						set ValExtensionproducer(value) { vm.model.ValExtensionproducer.updateValue(value) },
						get ValFormat() { return vm.model.ValFormat.value },
						set ValFormat(value) { vm.model.ValFormat.updateValue(value) },
						get ValId() { return vm.model.ValId.value },
						set ValId(value) { vm.model.ValId.updateValue(value) },
						get ValIdentifier() { return vm.model.ValIdentifier.value },
						set ValIdentifier(value) { vm.model.ValIdentifier.updateValue(value) },
						get ValInfoname() { return vm.model.ValInfoname.value },
						set ValInfoname(value) { vm.model.ValInfoname.updateValue(value) },
						get ValInwork() { return vm.model.ValInwork.value },
						set ValInwork(value) { vm.model.ValInwork.updateValue(value) },
						get ValIssuenumber() { return vm.model.ValIssuenumber.value },
						set ValIssuenumber(value) { vm.model.ValIssuenumber.updateValue(value) },
						get ValIssuetype() { return vm.model.ValIssuetype.value },
						set ValIssuetype(value) { vm.model.ValIssuetype.updateValue(value) },
						get ValLearneventcode() { return vm.model.ValLearneventcode.value },
						set ValLearneventcode(value) { vm.model.ValLearneventcode.updateValue(value) },
						get ValLearncode() { return vm.model.ValLearncode.value },
						set ValLearncode(value) { vm.model.ValLearncode.updateValue(value) },
						get ValModelidentcode() { return vm.model.ValModelidentcode.value },
						set ValModelidentcode(value) { vm.model.ValModelidentcode.updateValue(value) },
						get ValMonth() { return vm.model.ValMonth.value },
						set ValMonth(value) { vm.model.ValMonth.updateValue(value) },
						get ValPolicystatement() { return vm.model.ValPolicystatement.value },
						set ValPolicystatement(value) { vm.model.ValPolicystatement.updateValue(value) },
						get ValPublisher() { return vm.model.ValPublisher.value },
						set ValPublisher(value) { vm.model.ValPublisher.updateValue(value) },
						get ValRelation() { return vm.model.ValRelation.value },
						set ValRelation(value) { vm.model.ValRelation.updateValue(value) },
						get ValRights() { return vm.model.ValRights.value },
						set ValRights(value) { vm.model.ValRights.updateValue(value) },
						get ValSeccavat() { return vm.model.ValSeccavat.value },
						set ValSeccavat(value) { vm.model.ValSeccavat.updateValue(value) },
						get ValSeccocla() { return vm.model.ValSeccocla.value },
						set ValSeccocla(value) { vm.model.ValSeccocla.updateValue(value) },
						get ValSecurcla() { return vm.model.ValSecurcla.value },
						set ValSecurcla(value) { vm.model.ValSecurcla.updateValue(value) },
						get ValSource() { return vm.model.ValSource.value },
						set ValSource(value) { vm.model.ValSource.updateValue(value) },
						get ValSubject() { return vm.model.ValSubject.value },
						set ValSubject(value) { vm.model.ValSubject.updateValue(value) },
						get ValSystemdiffcode() { return vm.model.ValSystemdiffcode.value },
						set ValSystemdiffcode(value) { vm.model.ValSystemdiffcode.updateValue(value) },
						get ValTechname() { return vm.model.ValTechname.value },
						set ValTechname(value) { vm.model.ValTechname.updateValue(value) },
						get ValTitle() { return vm.model.ValTitle.value },
						set ValTitle(value) { vm.model.ValTitle.updateValue(value) },
						get ValType() { return vm.model.ValType.value },
						set ValType(value) { vm.model.ValType.updateValue(value) },
						get ValYear() { return vm.model.ValYear.value },
						set ValYear(value) { vm.model.ValYear.updateValue(value) },
					},
					Langu: {
						get ValLanguageisocode() { return vm.model.TableLanguLanguiso.value },
						set ValLanguageisocode(value) { vm.model.TableLanguLanguiso.updateValue(value) },
					},
					keys: {
						/** The primary key of the DSCPP table */
						get dscpp() { return vm.model.ValCoddscrp },
						/** The foreign key to the DMCDR table */
						get dmcdr() { return vm.model.ValCoddmcod },
						/** The foreign key to the LANGU table */
						get langu() { return vm.model.ValCodlangu },
						/** The foreign key to the COUNT table */
						get count() { return vm.model.ValCodcount },
						/** The foreign key to the COMPR table */
						get compr() { return vm.model.ValCodcompr },
						/** The foreign key to the COMPO table */
						get compo() { return vm.model.ValCodcompo },
					},
					get extraProperties() { return vm.model.extraProperties },
				},
                showMultiUploadModal: false,
			}
		},

		beforeRouteEnter(to, _, next)
		{
			// Called before the route that renders this component is confirmed.
			// Does NOT have access to `this` component instance, because
			// it has not been created yet when this guard is called!

			next((vm) => {
				vm.initFormProperties(to)
			})
		},

		beforeRouteLeave(to, _, next)
		{
			if (to.params.isControlled === 'true')
			{
				genericFunctions.setNavigationState(false)
				next()
			}
			else
				this.cancel(next)
		},

		beforeRouteUpdate(to, _, next)
		{
			if (to.params.isControlled === 'true')
				next()
			else
				this.cancel(next)
		},

		mounted()
		{
/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS FORM_CODEJS DSCPP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */

			// Listener global para capturar uploads de pasta de qualquer componente
			this.setupFolderUploadListener();

			// Expor função globalmente para que outros componentes possam chamar
			window.refreshDscppDocumentsList = () => {
				this.refreshDocumentsList();
			};
		},

		beforeUnmount() {
			// Limpar listeners quando o componente for desmontado
			if (this.folderUploadCompleteHandler) {
				document.removeEventListener('folder-upload-complete', this.folderUploadCompleteHandler);
			}
			if (this.domObserver) {
				this.domObserver.disconnect();
			}
			if (this.uploadPollingInterval) {
				clearInterval(this.uploadPollingInterval);
			}
		},

		methods: {
			/**
			 * Called before form init.
			 */
			async beforeLoad()
			{
				let loadForm = true

				// Execute the "Before init" triggers.
				const triggers = this.getTriggers(qEnums.triggerEvents.beforeInit)
				for (let trigger of triggers)
					await formFunctions.executeTriggerAction(trigger)

				this.emitEvent('before-load-form')

/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS BEFORE_LOAD_JS DSCPP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */

				return loadForm
			},

			/**
			 * Called after form init.
			 */
			async afterLoad()
			{
				// Execute the "After init" triggers.
				const triggers = this.getTriggers(qEnums.triggerEvents.afterInit)
				for (let trigger of triggers)
					await formFunctions.executeTriggerAction(trigger)

				this.emitEvent('after-load-form')

/* eslint-disable indent, vue/html-indent, vue/script-indent */
//Platform: VUE | Type: FORM_LOADED_JS | Module: TDS | Parameter: DSCPP | File:  | Order: 0
//BEGIN_MANUALCODE_CODMANUA:e895cb7c-29d2-4f88-a4c1-b6a1b730f0fe
				if (this.navigation?.previousLevel.params.previouslyRemovedRoute == 'form-DIMPO') {
					this.controls.DSCPP___DSCPPSYSTEMDI.addBlockSource('HISTORY')
                    if (qApi.emptyG(this.model.ValCoddmcod.value) == 0)
						this.controls.DSCPP___DMCDRCOMPLCOD.addBlockSource('HISTORY')
				}
				//if (this.navigation.getEntryValue('id_root') != undefined)
				//	debugger;
//END_MANUALCODE
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */
			},

			/**
			 * Called before an apply action is performed.
			 */
			async beforeApply()
			{
				let applyForm = true // Set to 'false' to cancel form apply.

				// Execute the "Before apply" triggers.
				const triggers = this.getTriggers(qEnums.triggerEvents.beforeApply)
				for (let trigger of triggers)
					await formFunctions.executeTriggerAction(trigger)

				const canSetDocums = await this.model.updateFilesTickets(true)

				if (canSetDocums)
				{
					applyForm = await this.model.setDocumentChanges()

					if (applyForm)
					{
						const results = await this.model.saveDocuments()
						applyForm = results.every((e) => e === true)
					}
				}

				this.emitEvent('before-apply-form')

/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS BEFORE_APPLY_JS DSCPP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */

				return applyForm
			},

			/**
			 * Called after an apply action is performed.
			 */
			async afterApply()
			{
				// Execute the "After apply" triggers.
				const triggers = this.getTriggers(qEnums.triggerEvents.afterApply)
				for (let trigger of triggers)
					await formFunctions.executeTriggerAction(trigger)

				this.emitEvent('after-apply-form')

/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS AFTER_APPLY_JS DSCPP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */
			},

			/**
			 * Called before the record is saved.
			 */
			async beforeSave()
			{
				let saveForm = true // Set to 'false' to cancel form saving.

				// Execute the "Before save" triggers.
				const triggers = this.getTriggers(qEnums.triggerEvents.beforeSave)
				for (let trigger of triggers)
					await formFunctions.executeTriggerAction(trigger)

				const canSetDocums = await this.model.updateFilesTickets()

				if (canSetDocums)
				{
					saveForm = await this.model.setDocumentChanges()

					if (saveForm)
					{
						const results = await this.model.saveDocuments()
						saveForm = results.every((e) => e === true)
					}
				}

				this.emitEvent('before-save-form')

/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS BEFORE_SAVE_JS DSCPP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */

				return saveForm
			},

			/**
			 * Called after the record is saved.
			 */
			async afterSave()
			{
				let redirectPage = true // Set to 'false' to cancel page redirect.

				// Execute the "After save" triggers.
				const triggers = this.getTriggers(qEnums.triggerEvents.afterSave)
				for (let trigger of triggers)
					await formFunctions.executeTriggerAction(trigger)

				this.emitEvent('after-save-form')

/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS AFTER_SAVE_JS DSCPP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */

				return redirectPage
			},

			/**
			 * Called before the record is deleted.
			 */
			async beforeDel()
			{
				let deleteForm = true // Set to 'false' to cancel form delete.

				this.emitEvent('before-delete-form')

/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS BEFORE_DEL_JS DSCPP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */

				return deleteForm
			},

			/**
			 * Called after the record is deleted.
			 */
			async afterDel()
			{
				let redirectPage = true // Set to 'false' to cancel page redirect.

				this.emitEvent('after-delete-form')

/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS AFTER_DEL_JS DSCPP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */

				return redirectPage
			},

			/**
			 * Called before leaving the form.
			 */
			async beforeExit()
			{
				let leaveForm = true // Set to 'false' to cancel page redirect.

				// Execute the "Before exit" triggers.
				const triggers = this.getTriggers(qEnums.triggerEvents.beforeExit)
				for (let trigger of triggers)
					await formFunctions.executeTriggerAction(trigger)

				this.emitEvent('before-exit-form')

/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS BEFORE_EXIT_JS DSCPP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */

				return leaveForm
			},

			/**
			 * Called after leaving the form.
			 */
			async afterExit()
			{
				// Execute the "After exit" triggers.
				const triggers = this.getTriggers(qEnums.triggerEvents.afterExit)
				for (let trigger of triggers)
					await formFunctions.executeTriggerAction(trigger)

				this.emitEvent('after-exit-form')

/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS AFTER_EXIT_JS DSCPP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */
			},

			/**
			 * Called whenever a field's value is updated.
			 * @param {string} fieldName The name of the field in the format [table].[field] (ex: 'person.name')
			 * @param {object} fieldObject The object representing the field in the model
			 * @param {any} fieldValue The value of the field
			 * @param {any} oldFieldValue The previous value of the field
			 */
			// eslint-disable-next-line
			onUpdate(fieldName, fieldObject, fieldValue, oldFieldValue)
			{
/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS DLGUPDT DSCPP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */

				this.afterFieldUpdate(fieldName, fieldObject)
			},

			/**
			 * Called whenever a field is unfocused.
			 * @param {*} fieldObject The object representing the field in the model
			 * @param {*} fieldValue The value of the field
			 */
			// eslint-disable-next-line
			onBlur(fieldObject, fieldValue)
			{
/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS CTRLBLR DSCPP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */

				this.afterFieldUnfocus(fieldObject, fieldValue)
			},

			/**
			 * Called whenever a control's value is updated.
			 * @param {string} controlField The name of the field in the controls that will be updated
			 * @param {object} control The object representing the field in the controls
			 * @param {any} fieldValue The value of the field
			 */
			// eslint-disable-next-line
			onControlUpdate(controlField, control, fieldValue)
			{
/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS CTRLUPD DSCPP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */

				this.afterControlUpdate(controlField, fieldValue)
			},
/* eslint-disable indent, vue/html-indent, vue/script-indent */
//Platform: VUE | Type: FUNCTIONS_JS | Module: TDS | Parameter: DSCPP | File:  | Order: 0
//BEGIN_MANUALCODE_CODMANUA:f7d02b58-f82d-45fd-918e-c9e0ed35ba56
            // Created by [HG] at [2025.04.23]
            // Updated by [HG] at [2025.06.04]
            /**
             * Função genérica para atualizar o controlo DSCPP___PSEUDFICWEB1_
             * Usada tanto para upload de ficheiros como para upload de pastas
             */
            refreshDocumentsList() {
                console.log('Atualizando lista de documentos DSCPP___PSEUDFICWEB1_...');

                // Refresh da tabela DOCUM após upload
                if (this.controls && this.controls.DSCPP___PSEUDFICWEB1_) {
                    if (this.controls.DSCPP___PSEUDFICWEB1_.refresh) {
                        this.controls.DSCPP___PSEUDFICWEB1_.refresh();
                    }
                    if (this.controls.DSCPP___PSEUDFICWEB1_.reloadList) {
                        this.controls.DSCPP___PSEUDFICWEB1_.reloadList();
                    }
                    console.log('Lista de documentos atualizada com sucesso');
                } else {
                    console.warn('Controlo DSCPP___PSEUDFICWEB1_ não encontrado');
                }
            },

            onMultiFilesUploaded() {
                // Usar a função genérica para atualizar a lista
                this.refreshDocumentsList();
                this.showMultiUploadModal = false;
            },

            /**
             * Manipulador de eventos de ação da tabela
             * @param {Object} action - Ação disparada pela tabela
             */
            handleTableRowAction(action) {
                console.log('Ação da tabela recebida:', action);
                if (action && action.id === 'insert') {
                    console.log('Abrindo modal de upload múltiplo');
                    this.showMultiUploadModal = true;
                }
            },

            /**
             * Alterna a visibilidade do modal de upload múltiplo
             * @param {boolean} show - Define se o modal deve ser exibido ou ocultado
             */
            toggleMultiUploadModal(show) {
                console.log('toggleMultiUploadModal chamado com show =', show);
                console.log('Valor atual de showMultiUploadModal antes da alteração:', this.showMultiUploadModal);
                this.showMultiUploadModal = show;
                console.log('Novo valor de showMultiUploadModal:', this.showMultiUploadModal);
            },

            /**
             * Configura listener global para capturar uploads de pasta
             */
            setupFolderUploadListener() {
                // Listener para eventos customizados de upload de pasta
                this.folderUploadCompleteHandler = (event) => {
                    console.log('Evento de upload de pasta capturado:', event.detail);
                    // Atualizar a lista após upload de pasta
                    setTimeout(() => {
                        this.refreshDocumentsList();
                    }, 500); // Pequeno delay para garantir que o backend processou
                };

                // Adicionar listener ao document
                document.addEventListener('folder-upload-complete', this.folderUploadCompleteHandler);

                // Também escutar por mudanças no DOM que possam indicar upload de pasta
                this.observeDocumentChanges();

                // Polling simples para detectar uploads de pasta
                this.startUploadPolling();
            },

            /**
             * Observa mudanças no DOM que possam indicar upload de pasta completo
             */
            observeDocumentChanges() {
                // Criar um observer para mudanças no DOM
                this.domObserver = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        // Verificar se foram adicionados novos elementos que possam indicar upload completo
                        if (mutation.type === 'childList') {
                            mutation.addedNodes.forEach((node) => {
                                if (node.nodeType === Node.ELEMENT_NODE) {
                                    // Procurar por indicadores de upload completo
                                    if (node.textContent &&
                                        (node.textContent.includes('Upload completed') ||
                                         node.textContent.includes('Upload concluído') ||
                                         node.textContent.includes('pasta carregada'))) {
                                        console.log('Upload de pasta detectado via DOM observer');
                                        setTimeout(() => {
                                            this.refreshDocumentsList();
                                        }, 1000);
                                    }
                                }
                            });
                        }
                    });
                });

                // Observar mudanças no body
                this.domObserver.observe(document.body, {
                    childList: true,
                    subtree: true
                });
            },

            /**
             * Inicia polling para detectar uploads de pasta
             */
            startUploadPolling() {
                // Verificar periodicamente se há indicadores de upload ativo
                this.uploadPollingInterval = setInterval(() => {
                    // Procurar por elementos que indiquem upload em progresso ou completo
                    const uploadIndicators = document.querySelectorAll([
                        '[class*="upload"]',
                        '[class*="progress"]',
                        '[id*="upload"]'
                    ].join(','));

                    let uploadCompleted = false;

                    uploadIndicators.forEach(element => {
                        const text = element.textContent || '';
                        if (text.includes('Upload completed') ||
                            text.includes('Upload concluído') ||
                            text.includes('100%') ||
                            text.includes('pasta carregada')) {

                            // Verificar se este é um upload recente (elemento criado nos últimos 5 segundos)
                            const now = Date.now();
                            if (!element._lastChecked || (now - element._lastChecked) > 5000) {
                                element._lastChecked = now;
                                uploadCompleted = true;
                            }
                        }
                    });

                    if (uploadCompleted) {
                        console.log('Upload de pasta detectado via polling');
                        this.refreshDocumentsList();
                    }
                }, 2000); // Verificar a cada 2 segundos
            }
//END_MANUALCODE
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */
		},

		watch: {
		}
	}
</script>
